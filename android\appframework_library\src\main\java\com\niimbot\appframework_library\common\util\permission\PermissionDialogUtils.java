package com.niimbot.appframework_library.common.util.permission;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import com.blankj.utilcode.util.PermissionUtils;
import com.niimbot.appframework_library.dialog.CustomDialog;
import com.niimbot.utiliylibray.util.PreferencesUtils;
import com.qyx.languagelibrary.utils.LanguageUtil;

/**
 * Created by <PERSON> on 2018/5/23.
 * 动态权限 提示Dialog工具类
 */

public class PermissionDialogUtils {

    public static void showCameraPermissionDialog(final Context context) {
        showCameraPermissionDialog(context, null);
    }

    public static void showGalleryPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
        showSDPermissionDialog(context, "app01291", "app01369", requestCode, null, listener);
    }
    public static void showSDPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
        showSDPermissionDialog(context, "app01368", "app01369", requestCode, null, listener);
    }
    public static void showSDPermissionDialog(final Context context, int requestCode, PermissionRequester requester, XPermissionUtils.OnPermissionListener listener) {
        showSDPermissionDialog(context, "app01368", "app01369", requestCode, requester, listener);
    }

    private static void showSDPermissionDialog(final Context context, String unGrantMessage, String deniedMessage, int requestCode, PermissionRequester requester, XPermissionUtils.OnPermissionListener listener) {
        String[] permissions = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ) {
            permissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};
        }
        if (context instanceof Activity) {
            if (!XPermissionUtils.hasGrantedPermission(permissions)) {
                if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE))) {
                    String[] finalPermissions = permissions;
                    String[] finalPermissions1 = permissions;
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText(unGrantMessage, context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(finalPermissions, false);
                            })
                            .setPositiveButton("app01304", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                if (requester != null) {
                                    requester.requestPermissions(finalPermissions1);
                                } else {
                                    XPermissionUtils.requestPermissions(context, requestCode, finalPermissions1, new XPermissionUtils.OnPermissionListener() {
                                        @Override
                                        public void onPermissionGranted() {
                                            listener.onPermissionGranted();
                                            confirmHasRequestPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                                        }

                                        @Override
                                        public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                                            listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                                            confirmHasRequestPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                                        }
                                    });
                                }
                            }).create().show();
                } else {
                    String[] finalPermissions2 = permissions;
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText(deniedMessage, context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(finalPermissions2, true);
                            })
                            .setPositiveButton("app01308", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(finalPermissions2, false);
                                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                intent.setData(Uri.parse("package:" + context.getPackageName()));
                                context.startActivity(intent);
                                resetHasRequestPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                            }).create().show();
                }

            } else {
                listener.onPermissionGranted();
            }
        }
    }


    public static void showCameraPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
        String[] permissions = new String[]{Manifest.permission.CAMERA};
        if (context instanceof Activity) {
            if (!XPermissionUtils.hasGrantedPermission(permissions)) {
                if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.CAMERA))) {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01306", context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, false);
                            })
                            .setPositiveButton("app01304", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                XPermissionUtils.requestPermissions(context, requestCode, permissions, new XPermissionUtils.OnPermissionListener() {
                                    @Override
                                    public void onPermissionGranted() {
                                        listener.onPermissionGranted();
                                        confirmHasRequestPermission(Manifest.permission.CAMERA);
                                    }

                                    @Override
                                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                                        listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                                        confirmHasRequestPermission(Manifest.permission.CAMERA);
                                    }
                                });
                            }).create().show();
                } else {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01307", context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, true);
                            })
                            .setPositiveButton("app01308", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                intent.setData(Uri.parse("package:" + context.getPackageName()));
                                context.startActivity(intent);
                                resetHasRequestPermission(Manifest.permission.CAMERA);
                            }).create().show();
                }

            } else {
                listener.onPermissionGranted();
            }
        }
    }

    public static void showCameraPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener, OnCustomDialogDismissListener onDismissListener) {
        String[] permissions = new String[]{Manifest.permission.CAMERA};
        if (context instanceof Activity) {
            if (!XPermissionUtils.hasGrantedPermission(permissions)) {
                if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.CAMERA))) {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01306", context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, false);
                            })
                            .setPositiveButton("app01304", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                XPermissionUtils.requestPermissions(context, requestCode, permissions, new XPermissionUtils.OnPermissionListener() {
                                    @Override
                                    public void onPermissionGranted() {
                                        listener.onPermissionGranted();
                                        confirmHasRequestPermission(Manifest.permission.CAMERA);
                                    }

                                    @Override
                                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                                        listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                                        confirmHasRequestPermission(Manifest.permission.CAMERA);
                                    }
                                });
                            }).create().show();
                } else {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01307", context))
                            .setDismissListener((dialog -> onDismissListener.onDismiss()))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, true);
                            })
                            .setPositiveButton("app01308", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                intent.setData(Uri.parse("package:" + context.getPackageName()));
                                context.startActivity(intent);
                                resetHasRequestPermission(Manifest.permission.CAMERA);
                            }).create().show();
                }

            } else {
                listener.onPermissionGranted();
            }
        }
    }

  public static void showCallPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
    String[] permissions = new String[]{Manifest.permission.CALL_PHONE};
    if (context instanceof Activity) {
      if (!XPermissionUtils.hasGrantedPermission(permissions)) {
        if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.CALL_PHONE))) {
          new CustomDialog.Builder((Activity) context)
            .setTitle(getText("app01305", context))
            .setMessage(getText("app100001784", context))
            .setNegativeButton("app00030", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              listener.onPermissionDenied(permissions, false);
            })
            .setPositiveButton("app01304", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              XPermissionUtils.requestPermissions(context, requestCode, permissions, new XPermissionUtils.OnPermissionListener() {
                @Override
                public void onPermissionGranted() {
                  listener.onPermissionGranted();
                  confirmHasRequestPermission(Manifest.permission.CALL_PHONE);
                }

                @Override
                public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                  listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                  confirmHasRequestPermission(Manifest.permission.CALL_PHONE);
                }
              });
            }).create().show();
        } else {
          new CustomDialog.Builder((Activity) context)
            .setTitle(getText("app01305", context))
            .setMessage(getText("app100001785", context))
            .setNegativeButton("app00030", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              listener.onPermissionDenied(permissions, true);
            })
            .setPositiveButton("app01308", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
              intent.setData(Uri.parse("package:" + context.getPackageName()));
              context.startActivity(intent);
              resetHasRequestPermission(Manifest.permission.CALL_PHONE);
            }).create().show();
        }

      } else {
        listener.onPermissionGranted();
      }
    }
  }

    public static void showSelectImagePermission(Context context, onGrantListener listener) {
        showCameraPermissionDialog(
                context,
                1011,
                new XPermissionUtils.OnPermissionListener() {
                    @Override
                    public void onPermissionGranted() {
                        showSDPermissionDialog(context, 1012,
                                new XPermissionUtils.OnPermissionListener() {
                                    @Override
                                    public void onPermissionGranted() {
                                        listener.onGrant(true);
                                    }

                                    @Override
                                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                                        listener.onGrant(false);
                                    }
                                });
                    }

                    @Override
                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                            listener.onGrant(false);
                    }
            }
        );
    }

    public static void showCameraPermissionDialog(final Context context, DialogInterface.OnClickListener cancelDialog) {
        if (context instanceof Activity) {
            new CustomDialog.Builder((Activity) context)
                    .setTitle(getText("app01305", context))
                    .setMessage(getText("app01307", context))
                    .setNegativeButton("app00030", (dialogInterface, i) -> {
                        dialogInterface.dismiss();
                    })
                    .setPositiveButton("app01308", (dialogInterface, i) -> {
                        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        intent.setData(Uri.parse("package:" + context.getPackageName()));
                        context.startActivity(intent);
                    }).create().show();
        }
    }

    public static void showVoicePermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
        String[] permissions = new String[]{Manifest.permission.RECORD_AUDIO};
        if (context instanceof Activity) {
            if (!XPermissionUtils.hasGrantedPermission(permissions)) {
                if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.RECORD_AUDIO))) {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01294", context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, false);
                            })
                            .setPositiveButton("app01304", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                XPermissionUtils.requestPermissions(context, requestCode, permissions, new XPermissionUtils.OnPermissionListener() {
                                    @Override
                                    public void onPermissionGranted() {
                                        listener.onPermissionGranted();
                                        confirmHasRequestPermission(Manifest.permission.RECORD_AUDIO);
                                    }

                                    @Override
                                    public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                                        listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                                        confirmHasRequestPermission(Manifest.permission.RECORD_AUDIO);
                                    }
                                });
                            }).create().show();
                } else {
                    new CustomDialog.Builder((Activity) context)
                            .setTitle(getText("app01305", context))
                            .setMessage(getText("app01295", context))
                            .setNegativeButton("app00030", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                listener.onPermissionDenied(permissions, true);
                            })
                            .setPositiveButton("app01308", (dialogInterface, i) -> {
                                dialogInterface.dismiss();
                                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                intent.setData(Uri.parse("package:" + context.getPackageName()));
                                context.startActivity(intent);
                                resetHasRequestPermission(Manifest.permission.RECORD_AUDIO);
                            }).create().show();
                }

            } else {
                listener.onPermissionGranted();
            }
        }
    }


    public static void showVoicePermissionDialog(final Context context) {
        if (context instanceof Activity) {
            new CustomDialog.Builder((Activity) context)
                    .setTitle(getText("app01305", context))
                    .setMessage(getText("app01295", context))
                    .setNegativeButton("app00030", (dialogInterface, i) -> {
                        dialogInterface.dismiss();
                    })
                    .setPositiveButton("app01308", (dialogInterface, i) -> {
                        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        intent.setData(Uri.parse("package:" + context.getPackageName()));
                        context.startActivity(intent);
                    }).create().show();
        }
    }


    public static void showSystemPermissionGuide(Activity context, String message) {
        new CustomDialog.Builder((Activity) context)
                .setTitle(getText("app01305", context))
                .setMessage(getText(message, context))
                .setNegativeButton("app00030", (dialogInterface, i) -> {
                    dialogInterface.dismiss();
                })
                .setPositiveButton("app01308", (dialogInterface, i) -> {
                    dialogInterface.dismiss();
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse("package:" + context.getPackageName()));
                    context.startActivity(intent);
                    resetHasRequestPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                }).create().show();
    }

  public static void showLocationPermissionDialog(final Context context, int requestCode, XPermissionUtils.OnPermissionListener listener) {
    String[] permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION};
    if (context instanceof Activity) {
      if (!XPermissionUtils.hasGrantedPermission(permissions)) {
        if (!(XPermissionUtils.hasAlwaysDeniedPermission(context, permissions) && hasRequestPermission(Manifest.permission.ACCESS_FINE_LOCATION))) {
          new CustomDialog.Builder((Activity) context)
            .setTitle(getText("app01305", context))
            .setMessage(getText("app100001907", context))
            .setNegativeButton("app00030", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              listener.onPermissionDenied(permissions, false);
            })
            .setPositiveButton("app01304", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              confirmHasRequestPermission(Manifest.permission.ACCESS_FINE_LOCATION);
              XPermissionUtils.requestPermissions(context, requestCode, permissions, new XPermissionUtils.OnPermissionListener() {
                @Override
                public void onPermissionGranted() {
                  listener.onPermissionGranted();
                }

                @Override
                public void onPermissionDenied(String[] deniedPermissions, boolean alwaysDenied) {
                  listener.onPermissionDenied(deniedPermissions, alwaysDenied);
                }
              });
            }).create().show();
        } else {
          new CustomDialog.Builder((Activity) context)
            .setTitle(getText("app01305", context))
            .setMessage(getText("app100001907", context))
            .setNegativeButton("app00030", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              listener.onPermissionDenied(permissions, true);
            })
            .setPositiveButton("app01308", (dialogInterface, i) -> {
              dialogInterface.dismiss();
              Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
              intent.setData(Uri.parse("package:" + context.getPackageName()));
              context.startActivity(intent);
              resetHasRequestPermission(Manifest.permission.ACCESS_FINE_LOCATION);
            }).create().show();
        }

      } else {
        listener.onPermissionGranted();
      }
    }
  }

    public static boolean hasRequestPermission(String permissionName){
     return PreferencesUtils.INSTANCE.getBoolean("tag_request_"+ permissionName, false);
    }

    public static void confirmHasRequestPermission(String permissionName) {
        PreferencesUtils.INSTANCE.put("tag_request_"+ permissionName, true);
    }

    public static void resetHasRequestPermission(String permissionName) {
        PreferencesUtils.INSTANCE.put("tag_request_"+ permissionName, false);
    }

    public static String getText(String appStr, Context context) {
        return LanguageUtil.findLanguageString(appStr, context);
    }

    public static Boolean isGrantedSDPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ) {
            return PermissionUtils.isGranted(new String[]{Manifest.permission.READ_MEDIA_IMAGES});
        } else {
            return PermissionUtils.isGranted(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE});
        }
    }

    public interface onGrantListener{
        void onGrant(boolean success);
    }

    public interface PermissionRequester{
        void requestPermissions(String[] permissions);
    }

    public interface OnCustomDialogDismissListener{
        void onDismiss();
    }
}
