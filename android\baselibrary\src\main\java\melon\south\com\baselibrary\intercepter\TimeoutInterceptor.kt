package melon.south.com.baselibrary.intercepter

import com.blankj.utilcode.util.ThreadUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.Constant.TIME_KEY
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSON
import com.niimbot.okgolibrary.okgo.response.LzyResponse
import com.niimbot.utiliylibray.util.PreferencesUtils
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.Protocol
import okhttp3.Response
import okhttp3.ResponseBody
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone

/**
 * @ClassName: NetworkIntercepter
 * @Author: Liuxiaowen
 * @Date: 2021/1/14 19:09
 * @Description: 网络拦截器
 */
class TimeoutInterceptor: Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response? = null
        var responseBody: ResponseBody? = null
        try {
            response = chain.proceed(request)
            val serverTime = response.headers["Date"]
            if (serverTime?.isNotBlank() == true && "null" != serverTime) {
                PreferencesUtils.put(TIME_KEY, response.headers["Date"])
               if(PreferencesUtils.getString("${LoginDataEnum.id}_template/myList").isNullOrEmpty()){
                   val timeForServerStr = PreferencesUtils.getString(TIME_KEY, "")
                   val pattern = "EEE, dd MMM yyyy HH:mm:ss z"
                   val dateFormat = SimpleDateFormat(pattern, Locale.US)
                   dateFormat.timeZone = TimeZone.getTimeZone("GMT")
                   val date = dateFormat.parse(timeForServerStr)
                   val calendar = Calendar.getInstance()
                   calendar.time = date
                   calendar.add(Calendar.MONTH, -6)

                   val sixMonthsAgo = calendar.time
                   val timestamp = sixMonthsAgo.time
                   PreferencesUtils.put("${LoginDataEnum.id}_template/myList", timestamp.toString())
               }
            }
            val status = response.code
            if (response.isSuccessful) {
                try{
                    responseBody = response.body
                    val bodyStr = responseBody?.string()
                    val lzyResponse = JSON.parseObject(bodyStr, LzyResponse::class.java)
                    val code = lzyResponse.code
                    return if (code == 50300) { //服务器停服
                        NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.OUT_OF_SERVICE)
                        responseBody = ResponseBody.create("text/plain;charset=utf-8".toMediaTypeOrNull(),"")
                        Response.Builder()
                            .code(50300)
                            .message("")
                            .request(request)
                            .body(responseBody)
                            .protocol(Protocol.HTTP_1_1)
                            .build()
                    } else {
                        NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.NETWORK_AVAILABLE)
                        response.newBuilder().body(bodyStr?.let {
                            ResponseBody.create(responseBody?.contentType(),it)
                        }).build()
                    }
                } catch(e: Exception) {e.printStackTrace()}
            } else {
                if (status == 503) {
                    try{
                        responseBody = response.body
                        val bodyStr = responseBody?.string()
                        val lzyResponse = JSON.parseObject(bodyStr, LzyResponse::class.java)
                        val code = lzyResponse.code
                        return if (code == 503000) { //服务器熔断
                            NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.OUT_OF_SERVICE)
                            responseBody = ResponseBody.create("text/plain;charset=utf-8".toMediaTypeOrNull(),"")
                            Response.Builder()
                                .code(503000)
                                .message("")
                                .request(request)
                                .body(responseBody)
                                .protocol(Protocol.HTTP_1_1)
                                .build()
                        } else {
                            NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.CONNECT_TIMEOUT)
                            response.newBuilder().body(bodyStr?.let {
                                ResponseBody.create(responseBody?.contentType(),it)
                            }).build()
                        }
                    } catch(e: Exception) {e.printStackTrace()}
                } else if (status == 401 || status == 403) {
                    ThreadUtils.runOnUiThread {
                        if (LoginDataEnum.isLogin) {
                            showToast("app01274")
                            LoginDataEnum.unLogin()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.CONNECT_TIMEOUT)
        } finally {
            responseBody?.close()
        }
        return response?: Response.Builder()
            .code(404)
            .message("")
            .request(request)
            .body(ResponseBody.create("text/plain;charset=utf-8".toMediaTypeOrNull(),""))
            .protocol(Protocol.HTTP_1_1)
            .build()
    }
}
