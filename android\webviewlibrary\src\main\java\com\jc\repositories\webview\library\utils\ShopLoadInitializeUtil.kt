
import android.app.Activity
import android.app.Application
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.text.TextUtils
import android.util.Base64
import android.view.View
import android.view.WindowManager
import android.webkit.ConsoleMessage
import android.webkit.GeolocationPermissions
import android.webkit.JavascriptInterface
import android.webkit.JsResult
import android.webkit.SslErrorHandler
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebChromeClient.FileChooserParams
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import com.blankj.utilcode.util.LogUtils
import com.jc.repositories.webview.library.NiimbotConstant
import com.jc.repositories.webview.library.eventbus.RefreshShopTokenEvent
import com.jc.repositories.webview.library.eventbus.ScanExchangeSuccessEvent
import com.jc.repositories.webview.library.eventbus.ShopStoreBeanEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.utils.GlideEngine
import com.jc.repositories.webview.library.utils.showFailedView
import com.jc.repositories.webview.library.view.JCWebViewFactory
import com.jc.repositories.webview.library.view.LollipopFixedWebView
import com.jc.repositories.webview.library.view.NewStoreFragment
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType.ofImage
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.messagebus.config.LeIntentConfig
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.template.SearchScanActivityConfig
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.PictureUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.fastjson.JSONObject
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.SystemUtil
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.json2Any
import com.southcity.watermelon.util.logD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.baselibrary.eventbus.ShopBubbleChangeEvent
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.OtherAppUtil
import melon.south.com.baselibrary.util.ToastInstance
import melon.south.com.baselibrary.util.showToast
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.lang.ref.WeakReference


/**
 * <AUTHOR>
 * @date 2019/12/6.
 * email：<EMAIL>
 * description：
 */

class ShopLoadInitializeUtil private constructor(var app: Application) {

    companion object {

        private lateinit var INSTANCE: ShopLoadInitializeUtil
        @Synchronized fun get(): ShopLoadInitializeUtil {
            if (!this::INSTANCE.isInitialized || null == INSTANCE) {
                initialize(SuperUtils.superContext as Application)
            }
            return INSTANCE
        }

        fun initialize(app: Application) {
            if (!this::INSTANCE.isInitialized || INSTANCE == null) {
                INSTANCE = ShopLoadInitializeUtil(app)
                JCWebViewFactory.getInstance().preload()
            }
        }
    }


    private var windowManager: WindowManager? = null
    private var fullScreenView: View? = null
    private var scanType = 1
    private var hasSetShopToken = false
    init {
        LogUtils.d("ShopLoadInitializeUtil init()")
        // loadStoreRes()
    }

    private var mActivity: WeakReference<Activity>? = null
    var x5WebView: LollipopFixedWebView? = null
    var loadSuccess = false

    fun initWevView(activity: Activity) {
        this.mActivity = WeakReference(activity)
        if (null == x5WebView) {
            x5WebView = JCWebViewFactory.getInstance().getX5Webview(mActivity?.get())
        }

        x5WebView?.settings?.javaScriptEnabled = true
        val originUA = x5WebView?.settings?.userAgentString
        x5WebView?.settings?.userAgentString = originUA + " " + SystemUtil.getUserAgent(mActivity?.get())
        var userAgentStr = x5WebView?.settings?.userAgentString
        LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")
        x5WebView?.settings?.cacheMode = WebSettings.LOAD_NO_CACHE
        x5WebView?.webChromeClient = this.MyWebChromeClient()
        x5WebView?.webViewClient = this.MyWebViewClient()
//        x5WebView?.postVisualStateCallback(-1L, object : WebView.VisualStateCallback(){
//            override fun onComplete(p0: Long) {
//                loadSuccess = true
//            }
//        })
        mActivity?.get()?.let {
            x5WebView?.addJavascriptInterface(ShopScriptinterface(), "android")
            x5WebView?.loadUrl(appendUrl())
        }
    }

    private fun appendUrl(): String {
        var url = "${ShopManager.SHOP_CONSUMABLES}?"
        url += "token=${HttpTokenUtils.getShopToken()}&"
        url += "version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&preloading=1&entrance_type_id=1&jumpSource=y_page_main"
        url = ShopManager.getFinalUrl(url)
        logD("appendUrl= ", url)
        return url
    }


    /**
     * 重新加载load
     */
    var mJsCallLisener: JSCallLisener? = null

    fun setReInitWevView(activity: Activity) {
        this.mActivity = WeakReference(activity)
        if (null == x5WebView) {
            x5WebView = JCWebViewFactory.getInstance().getX5Webview(mActivity?.get())
        }
        windowManager = mActivity?.get()?.windowManager
        val originUA = x5WebView?.settings?.userAgentString
        x5WebView?.settings?.userAgentString = originUA + " " + SystemUtil.getUserAgent(mActivity?.get())
        var userAgentStr = x5WebView?.settings?.userAgentString
        LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")
        x5WebView?.settings?.cacheMode = WebSettings.LOAD_NO_CACHE
        x5WebView?.settings?.javaScriptEnabled = true
        x5WebView?.webChromeClient = this.MyWebChromeClient()
        x5WebView?.webViewClient = this.MyWebViewClient()
//        x5WebView?.postVisualStateCallback(-1L, object : WebView.VisualStateCallback(){
//            override fun onComplete(p0: Long) {
//                loadSuccess = true
//            }
//        })
        // JS调用Android的方法
        mActivity?.get()?.let {
            x5WebView?.addJavascriptInterface(ShopScriptinterface(), "android")
        }
    }

    fun setReInit(jsCallLisener: JSCallLisener) {
        this.mJsCallLisener = jsCallLisener
    }

    fun loadUrl(url: String) {
        x5WebView?.loadUrl(url)
    }

    interface JSCallLisener {
        fun onShowFileChooser(filePathCallback: ValueCallback<Array<Uri>>?, fileChooserParams: FileChooserParams?)
        fun onProgressChanged(newProgress: Int)
        fun shouldOverrideUrlLoading(url: String)
        fun onPageFinished(url: String)
        fun onShowTab(str: String)
    }

    inner class ShopScriptinterface constructor() {
        @JavascriptInterface
        fun loginOnToApp() {
            logI("wangxuhao","loginOnToApp call")

            GlobalScope.launch(Dispatchers.Main) {
                mActivity?.get()?.let {
                    LoginDataEnum.forceLogin(it) {
                        EventBus.getDefault().post(RefreshShopTokenEvent())
                    }
                }
            }

        }
        @JavascriptInterface
        fun refreshUserInfo() {
            EventBus.getDefault().post(ScanExchangeSuccessEvent())
        }

        @JavascriptInterface
        fun backHome(result: String) {
            mActivity?.get()?.finish()
        }

        @JavascriptInterface
        fun jcdy_StatusBar_Color(data: String) {
            if (!AppDataUtil.isStoreFragmentShow) {
                return
            }
            /**
             * 0: 深色（文字白色）
             * 1：浅色（文字黑色）
             */
            //val bool = (activity as MainActivity).mIndex == 1
            // LogUtils.e("jcdy_StatusBar_Color bool= $bool")
            LogUtils.e("jcdy_StatusBar_Color data = $data    ${AppDataUtil.isStoreFragmentShow}")
            GlobalScope.launch(Dispatchers.Main) {
                try {
                    val bean = json2Any(data, NewStoreFragment.StatusBean::class.java)
                        ?: NewStoreFragment.StatusBean()
                    // (activity as MainActivity).mStatusBarColorCache = bean
                    EventBus.getDefault().post(ShopStoreBeanEvent(bean));
                    mActivity?.get()?.let {
                        AppUtils.setStatusBarLightColor(
                            it,
                            Color.parseColor(bean.color),
                            bean.type == "1"
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        @JavascriptInterface
        fun jcdy_shopScan() {
            LogUtils.e("jcdy_shopScan")
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {
                    mActivity?.get()?.let { context ->
                        PermissionDialogUtils.showCameraPermissionDialog(
                            context,
                            RequestCode.MORE,
                            object : XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {
                                    LeMessageManager.getInstance().dispatchMessage(
                                        LeMessage(
                                            LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                                            SearchScanActivityConfig(context).apply {
                                                intent.putExtra("trackSource", 5)
                                                setIntentFlag(LeIntentConfig.IntentFlag.START_ACTIVITY_FOR_RESULT)
                                                intent.putExtra(com.niimbot.baselibrary.Constant.SCAN_CREATE, true)
                                                intent.putExtra(
                                                    NiimbotConstant.TAG_TITLE,
                                                    LanguageUtil.checkLanguage("app01099")
                                                )
                                                intent.putExtra(
                                                    NiimbotConstant.TAG_CONTENT,
                                                    LanguageUtil.checkLanguage("app01098")
                                                )
                                                setRequestCode(222) { resultCode, intent ->
                                                    if (resultCode == Activity.RESULT_OK) {
                                                        val bundle = intent?.extras
                                                        scanType = bundle?.getInt("scan_type") ?: 1
                                                        val resultStr =
                                                            bundle?.getString(com.niimbot.baselibrary.Constant.newScan_from.RESULT_QRCODE_STRING)
                                                        x5WebView?.loadUrl("javascript:returnScanResult('$resultStr')")
                                                    }
                                                }

                                            })
                                    )
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<String>,
                                    alwaysDenied: Boolean
                                ) {
                                    com.niimbot.appframework_library.utils.showToast("app01309")
                                }
                            })
                    }
                } else {
                    showToast("app01139")
                }
            }
        }

        @JavascriptInterface
        fun jcdy_shopScan(str: String) {
            LogUtils.e("jcdy_shopScan")
            jcdy_shopScan()
        }

        @JavascriptInterface
        fun jcdy_Enter_Detail(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            //进入订单详情
            GlobalScope.launch(Dispatchers.Main) {
                //                AppUtils.setSuspendStatusBar(activity)
//                AppUtils.setStatusBarColor(activity, Color.parseColor("#6494BC"))
            }
        }

        @JavascriptInterface
        fun jcdy_Leave_Detail(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            GlobalScope.launch(Dispatchers.Main) {
                //                AppUtils.setStatusBarLightColor(activity, Color.WHITE)
            }
        }

        @JavascriptInterface
        fun jcdy_Show_Tabbar(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            GlobalScope.launch(Dispatchers.Main) {
                mJsCallLisener?.onShowTab(str)
            }
        }

        @JavascriptInterface
        fun getUserToken(): String {
            return "token=${
                com.niimbot.baselibrary.user.LoginDataEnum.userModule?.token
                ?: ""}&version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001"
        }

        @JavascriptInterface
        fun getPrinterType(): String{
            val lastDeviceName = BluetoothUtil.getLastDeviceName()
            com.blankj.utilcode.util.LogUtils.e("js获取lastDeviceName： $lastDeviceName")
            return lastDeviceName
        }

        @JavascriptInterface
        fun gotoAmazonShop(productPath: String, productWebPath: String) {
            mActivity?.get()?.let {
                try{
                    var intent = Intent(Intent.ACTION_VIEW)
                    intent.data = Uri.parse(if (OtherAppUtil.isAmazonShopInstalled(it))
                        productPath
                    else
                        productWebPath
                    )
                    it.startActivity(intent)
                } catch(e: Exception) {e.printStackTrace()}
            }
        }

        @JavascriptInterface
        fun nativeFunction(url: String) {
            mActivity?.get()?.let { NiimbotGlobal.routingByScheme(it, url) }
        }

        @JavascriptInterface
        fun tabShopShowNumChanged(num: String) {
            LogUtils.e("======tabShopShowNumChanged: $num")
            try{
                var value = num.toInt()
                EventBus.getDefault().post(ShopBubbleChangeEvent(value))
            } catch(e: Exception) {e.printStackTrace()}
        }

        @JavascriptInterface
        fun scanCodeSearchLabelResult(body: Boolean) {
            var jsonObject = JSONObject()
            jsonObject["action"] = "scanResultByShop"
            jsonObject["result"] = body
            jsonObject["type"] = scanType
            EventBus.getDefault().post(any2Json(jsonObject))
        }

        @JavascriptInterface
        fun getAlbum(compressSizeByte: Int) {
            LogUtils.e("======getAlbum, compressSizeByte = $compressSizeByte")
            if (android.os.Environment.getExternalStorageState() != android.os.Environment.MEDIA_MOUNTED) {
                showToast("app01195")
                return
            }
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {
                    mActivity?.get()?.let { context ->
                        PermissionDialogUtils.showGalleryPermissionDialog(
                            context,
                            RequestCode.MORE,
                            object : XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {
                                    PictureSelector.create(context)
                                        .openGallery(ofImage())
                                        .setMaxSelectNum(1)
                                        .setSelectionMode(SelectModeConfig.SINGLE)
                                        .setImageEngine(GlideEngine.createGlideEngine())
                                        .setLanguage(
                                            TextHookUtil.getInstance().getPictureSelectorLanguage()
                                        )
                                        .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                            override fun onResult(result: ArrayList<LocalMedia?>?) {
                                                handleChoosePictureResult(result, compressSizeByte)
                                            }

                                            override fun onCancel() {
                                                LogUtils.e("======getAlbum: onCancel")
                                            }
                                        })
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<String>,
                                    alwaysDenied: Boolean
                                ) {
                                    showToast("app01298")
                                }
                            })
                    }
                }
            }
        }

        @JavascriptInterface
        fun getPhotograph(compressSizeByte: Int){
            LogUtils.e("======getPhotograph, compressSizeByte = $compressSizeByte")
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {
                    mActivity?.get()?.let { context ->
                        PermissionDialogUtils.showCameraPermissionDialog(
                            context,
                            RequestCode.MORE,
                            object : XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {
                                    PictureSelector.create(context)
                                        .openCamera(ofImage())
                                        .setLanguage(TextHookUtil.getInstance().getPictureSelectorLanguage())
                                        .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                            override fun onResult(result: ArrayList<LocalMedia?>?) {
                                                handleChoosePictureResult(result, compressSizeByte)
                                            }

                                            override fun onCancel() {
                                                LogUtils.e("======getPhotograph: onCancel")
                                            }
                                        })
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<String>,
                                    alwaysDenied: Boolean
                                ) {
                                    showToast("app01309")
                                }
                            })
                    }
                } else {
                    showToast("app01139")
                }
            }
        }

        @JavascriptInterface
        fun loadingEnd(type: Int){
            GlobalScope.launch(Dispatchers.Main){
                val shopToken = HttpTokenUtils.getShopToken()
//                val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
                val deviceId = BuriedHelper.getEventAnonymousId()
                val userId = LoginDataEnum.id.toString()
                val platform = "Android_YDY"
                x5WebView?.evaluateJavascript("javascript:AppSetToken('${shopToken}', '${deviceId}', '${userId}', '${platform}')", null)
                hasSetShopToken = true
            }
        }

        @JavascriptInterface
        fun requestLocationPermission() {
            MainScope().launch {
                mActivity?.get()?.let { context ->
                    PermissionDialogUtils.showLocationPermissionDialog(context, RequestCode.MORE, object : XPermissionUtils.OnPermissionListener {
                        override fun onPermissionGranted() {
                            x5WebView?.evaluateJavascript("javascript:hasLocationPermission(1)", null)
                        }

                        override fun onPermissionDenied(
                            deniedPermissions: Array<out String>?,
                            alwaysDenied: Boolean
                        ) {
                            x5WebView?.evaluateJavascript("javascript:hasLocationPermission(0)", null)
                        }

                    })
                }
            }
        }

        @JavascriptInterface
        fun getClipboardContent(): String {
            val clipboardManager = mActivity?.get()?.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager ?: return ""
            val data = clipboardManager.primaryClip ?: return ""
            if (data.itemCount > 0) {
                if (data.getItemAt(0) == null || data.getItemAt(0).text == null || TextUtils.isEmpty(
                        data.getItemAt(0).text.toString()
                    )
                ) {
                    return ""
                }
                return data.getItemAt(0).text.toString()
            }
            return ""
        }
    }

    private inner class MyWebChromeClient : WebChromeClient() {

        override fun onConsoleMessage(p0: ConsoleMessage?): Boolean {
            return true
        }

        override fun onJsAlert(p0: WebView?, p1: String?, p2: String?, p3: JsResult?): Boolean {
            return false
        }

        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            mJsCallLisener?.onShowFileChooser(filePathCallback, fileChooserParams)
            return true
        }

        override fun onProgressChanged(p0: WebView?, newProgress: Int) {
            mJsCallLisener?.onProgressChanged(newProgress)
            loadSuccess = newProgress > 80
        }

        override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
            super.onShowCustomView(view, callback)

            // 此处的 view 就是全屏的视频播放界面，需要把它添加到我们的界面上
            windowManager?.addView(
                view,
                WindowManager.LayoutParams(WindowManager.LayoutParams.TYPE_APPLICATION)
            )
            // 去除状态栏和导航按钮
            if (view != null) {
                fullScreen(view)
            }
            fullScreenView = view
        }

        override fun onHideCustomView() {
            super.onHideCustomView()
            // 退出全屏播放，我们要把之前添加到界面上的视频播放界面移除
            windowManager?.removeViewImmediate(fullScreenView);
            fullScreenView = null;
        }

        override fun onGeolocationPermissionsShowPrompt(
            origin: String?,
            callback: GeolocationPermissions.Callback?
        ) {
            callback?.invoke(origin, true, false)

        }
    }

    private inner class MyWebViewClient : WebViewClient() {

        override fun onLoadResource(p0: WebView?, pUrl: String?) {
            super.onLoadResource(p0, pUrl)
            LogUtils.e("onLoadResource")
        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            LogUtils.e("onPageStarted: $url")
        }

        override fun onPageFinished(view: WebView?, url: String) {
            super.onPageFinished(view, url)
            mJsCallLisener?.onPageFinished(url)
            LogUtils.e("onPageFinished")
        }

        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            if (url == null) return false
            return isPay(view, url)
        }

        override fun onReceivedSslError(
            view: WebView?,
            handler: SslErrorHandler?,
            error: SslError?
        ) {
            super.onReceivedSslError(view, handler, error)
            handler?.proceed()
        }

        override fun onReceivedError(
            view: WebView?,
            request: WebResourceRequest?,
            error: WebResourceError?
        ) {
            super.onReceivedError(view, request, error)
            LogUtils.e("onReceivedError: url: ${view?.url}, request: ${any2Json(request)}, error: ${any2Json(error)}")
            if (null != view && request?.isForMainFrame == true && (
                        error?.errorCode == WebViewClient.ERROR_CONNECT || error?.errorCode == WebViewClient.ERROR_HOST_LOOKUP)) {
                view?.let {
                    if (it.url?.contains(ShopManager.SHOP_ROOT, true) == true) it.showFailedView()
                }
            }
        }

        override fun onReceivedHttpError(
            view: WebView?,
            request: WebResourceRequest?,
            errorResponse: WebResourceResponse?
        ) {
            super.onReceivedHttpError(view, request, errorResponse)
        }

    }

    private fun isPay(view: WebView?, url: String): Boolean {
        LogUtils.e("shouldOverrideUrlLoading()--> Url: $url")
        if (url.contains("weixin://wap/pay?") || url.contains("http://weixin/wap/pay")) {
            try {
                this.mActivity?.get()?.startActivity(Intent("android.intent.action.VIEW", Uri.parse(url)))
                return true
            } catch (e: Exception) {
                ToastInstance.INSTANCE.showToast("app01193")
            }
        } else if (url.contains("wx.tenpay.com")) {
            var extraHeaders = HashMap<String, String>()
            extraHeaders["Referer"] = ShopManager.WX_PAY
            view?.loadUrl(url, extraHeaders)
            return true
        } else {
            if(url.startsWith("alipays:") || url.startsWith("alipay")) {
                try {
                    mActivity?.get()?.startActivity( Intent ("android.intent.action.VIEW", Uri.parse(url)))
                    return true
                } catch (e: Exception) { }
            } else {
                val tokenSuffix = "?token=${HttpTokenUtils.getShopToken()}"
//                view?.loadUrl(if (url.contains(ShopManager.SHOP_ROOT.replace("http://", "")) && !url.contains(tokenSuffix)) "$url$tokenSuffix" else url)
                view?.loadUrl(url)
                LogUtils.e("--> Url: $url")
            }
        }
        return false
    }

    private fun fullScreen(view: View) {
        if (Build.VERSION_CODES.KITKAT == Build.VERSION.SDK_INT ) {
            view.systemUiVisibility = (View.SYSTEM_UI_FLAG_LOW_PROFILE
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION)
        } else {
            view.systemUiVisibility = (View.SYSTEM_UI_FLAG_LOW_PROFILE
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION)
        }
    }

    private fun handleChoosePictureResult(result: ArrayList<LocalMedia?>?, compressSizeByte: Int){
        val compressSize = compressSizeByte / 1024
        val imagePath = result?.getOrNull(0)?.realPath
        if(imagePath.isNullOrEmpty()){
            return
        }
        val compressDir = File(SuperUtils.superContext.filesDir, "store/image_compress/").absolutePath
        GlobalScope.launch(Dispatchers.Main){
            x5WebView?.evaluateJavascript("javascript:imageLoading()", null)
            val base64Data = withContext(Dispatchers.IO){
                val finalImagePath = PictureUtils.compressPictureIfNecessary(imagePath, compressDir, compressSize)
                fileToBase64(finalImagePath)
            }
            // if(base64Data.isNotEmpty()){
                x5WebView?.evaluateJavascript("javascript:imageDataCallback('${base64Data}')", null)
            // }
        }
    }

    /**
     * 将图片转换成Base64编码的字符串
     */
    private fun fileToBase64(path: String): String {
        if (path.isNullOrEmpty() || !File(path).exists()) {
            return ""
        }
        var inputStream: InputStream? = null
        val data: ByteArray
        try {
            inputStream = FileInputStream(path)
            //创建一个字符流大小的数组。
            data = ByteArray(inputStream.available())
            //写入数组
            inputStream.read(data)
            //用默认的编码格式进行编码
            return Base64.encodeToString(data, Base64.NO_WRAP)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close()
                }
                catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return ""
    }


    /**
     * 调用js 埋点
     */
    fun appUserClickEvent() {
        val params = "javascript:appUserClickEvent('y_page_main')"
        x5WebView?.loadUrl(params)
    }

    fun destroyWebView() {
        if (fullScreenView != null) {
            windowManager?.removeViewImmediate(fullScreenView);
            fullScreenView = null
        }
        x5WebView?.let {
            JCWebViewFactory.getInstance().recycleWebView(x5WebView)
        }
    }

    fun handleLoginOut(){
        if(hasSetShopToken){
//            val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
            val deviceId = BuriedHelper.getEventAnonymousId()
            val userId = LoginDataEnum.id.toString()
            val platform = "Android_YDY"
            x5WebView?.evaluateJavascript("javascript:AppSetToken('', '${deviceId}', '${userId}', '${platform}')", null)
            hasSetShopToken = false
        }
        EventBus.getDefault().post(ShopBubbleChangeEvent(0))
    }
}
