package com.jc.repositories.webview.library.view

import ShopLoadInitializeUtil
import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebChromeClient.FileChooserParams
import android.widget.FrameLayout
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.content.FileProvider
import com.andsync.xpermission.XPermissionUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.jc.repositories.webview.library.R
import com.jc.repositories.webview.library.databinding.ActivityNiimbotBaseWebviewBinding
import com.jc.repositories.webview.library.databinding.FragmentStoreNewBinding
import com.jc.repositories.webview.library.eventbus.AppLifecycleEvent
import com.jc.repositories.webview.library.eventbus.RefreshShopTokenEvent
import com.jc.repositories.webview.library.eventbus.ShowTabEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.shop.ShopType
import com.niimbot.appframework_library.BaseRootActivity
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.dialog.SheetDialog
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.LogUtils
import com.southcity.watermelon.util.logE
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.baselibrary.base.BasePagerFragment
import melon.south.com.baselibrary.eventbus.MeBubbleChangeEvent
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.RxTimer
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import com.jc.repositories.webview.library.utils.WebFileChooserHelper

class NewStoreFragment : BasePagerFragment(), WebFileChooserHelper.Host {

    private var url: String = ""
    private var banner: String? = null
    private var webView : LollipopFixedWebView?= null
    private var mViewParent: FrameLayout? = null
    private var mView : View ?= null
    private var shopLoadInitializeUtil : ShopLoadInitializeUtil ?= null

    val FILE_CHOOSER_REQUEST_CODE = 1
    private var cameraPhotoPath: String? = null
    private var cameraVideoPath: String? = null

    private lateinit var binding: FragmentStoreNewBinding

    override fun getLayoutId() = R.layout.fragment_store_new
    fun setBannerUrl(bannerUrl: String) {
        banner = bannerUrl
        LogUtils.d("setBannerUrl banner= ", banner)
        logE("url", "setBannerUrl: $url")
        banner?.let {
            if (!TextUtils.isEmpty(it)) {
                webView?.loadUrl(it)
            }
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun init(view: View) {
        EventBus.getDefault().register(this)
        binding = FragmentStoreNewBinding.bind(view)
        this.mView = view
        init()
        ShopManager.resetShopWebViewListener = {
            if (mViewParent != null && !activity?.isFinishing!! && this.mView != null) {
                reInit()
                resetIndex()
                url += "&preloading=1"
                LogUtils.d("resetIndex preload= ", url)
                logE("url", "loginStatusChange: $url")
                webView?.loadUrl(url)
                initEvent(this.mView!!)
            }
        }
    }

    private fun getStatusBarHeight(): Int {
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        return resources.getDimensionPixelSize(resourceId)
    }

    private fun initView(){
        webView = shopLoadInitializeUtil?.x5WebView
        mViewParent = this.mView?.findViewById(R.id.webViewFl)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webView?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        } else {
            webView?.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        }
        resetIndex()
        mViewParent?.removeAllViews()
        if(webView != null) {
            try{
                (webView?.parent as? ViewGroup)?.removeAllViews()
                mViewParent?.addView(webView, FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.FILL_PARENT,
                        FrameLayout.LayoutParams.FILL_PARENT))
            } catch(e: Exception) {e.printStackTrace()}
        }
//        if (TextHookUtil.getInstance().isChina()) {
            //切换语言会因为webView 加载页面后导致状态栏颜色变更
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                val layoutParams = webView?.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.setMargins(0, getStatusBarHeight(), 0, 0)
                webView?.layoutParams = layoutParams
            }
            com.niimbot.baselibrary.user.LoginDataEnum.registerChange(this)
            if (isVisible) {
                showIndexOrBanner()
            }
//        }
    }

    private fun init(){
        com.blankj.utilcode.util.LogUtils.e("NewStoreFragment init()")
        shopLoadInitializeUtil = ShopLoadInitializeUtil.get()
        shopLoadInitializeUtil?.setReInitWevView(requireActivity())
        initView()
//        try{
//            activity?.window?.let { KeyboardUtils.fixAndroidBug5497(it) }
//        } catch(e: Exception) {e.printStackTrace()}
    }

    private fun reInit(){
        com.blankj.utilcode.util.LogUtils.e("NewStoreFragment reInit()")
        shopLoadInitializeUtil = ShopLoadInitializeUtil.get()
        shopLoadInitializeUtil?.initWevView(requireActivity())
        initView()
    }

    private fun showIndexOrBanner() {
        if (banner.isNullOrEmpty()) {
            webView?.loadUrl(url)
        } else {
            webView?.loadUrl(banner!!)
        }
        webView?.clearHistory()
        banner = null
        resetIndex()
    }

    override fun onLoginStatusChange() {
        logI("NewStoreFragment", "onLoginStatusChange()")
        if(!AppDataUtil.shopIsService){
            resetIndex()
        }
        if(!LoginDataEnum.isLogin){
            shopLoadInitializeUtil?.handleLoginOut()
        }
    }

    private fun resetIndex() {
        url = "${ShopManager.shopHome}?token=${HttpTokenUtils.getShopToken()}&version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&entrance_type_id=1&jumpSource=y_page_main"
        url = ShopManager.getFinalUrl(url)
        logI("NewStoreFragment", "resetIndex: shopHome = ${ShopManager.shopHome}")
        logI("NewStoreFragment", "resetIndex: url = $url")
    }

    override fun initEvent(view: View) {
        ShopLoadInitializeUtil.get().setReInit(object :ShopLoadInitializeUtil.JSCallLisener{
            override fun onProgressChanged(newProgress: Int) {
                doLoading(newProgress)
            }

            override fun onShowFileChooser(filePathCallbackNew: ValueCallback<Array<Uri>>?, fileChooserParams: FileChooserParams?) {
                filePathCallback = filePathCallbackNew
                openFileChooseProcess(fileChooserParams)
            }

            override fun shouldOverrideUrlLoading(url: String) {
            }

            override fun onPageFinished(url: String) {
                if (url == <EMAIL>) {
                     webView?.clearHistory()
                 }
            }

            override fun onShowTab(str: String) {
                LogUtils.d("onShowTab str = ${str}")
                val flag = try {
                    str.toInt()
                } finally {
                    0
                }
                EventBus.getDefault().post(ShowTabEvent(flag == 1))
//                if (activity is MainActivity) {
//                    if(!isHidden){
//                        (activity as MainActivity).showTab(flag == 1)
//                    }
//                }
            }
        })
    }

    private fun doLoading(newProgress : Int){
        if(binding.progressBar != null) {
            if (newProgress >= 80) {
                binding.progressBar.visibility = View.GONE
                webView?.visibility = View.VISIBLE
            } else {
                binding.progressBar.visibility = View.VISIBLE
                binding.progressBar.progress = newProgress
            }
        }
    }

    private var filePathCallback: ValueCallback<Array<Uri>>? = null
    private fun openFileChooseProcess(fileChooserParams: WebChromeClient.FileChooserParams?) {
        WebFileChooserHelper.openFileChooseProcess(
            host = this,
            fileChooserParams = fileChooserParams,
            filePathCallback = filePathCallback,
            FILE_CHOOSER_REQUEST_CODE = FILE_CHOOSER_REQUEST_CODE,
            onCameraPhotoPath = { path -> cameraPhotoPath = path },
            onCameraVideoPath = { path -> cameraVideoPath = path }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == FILE_CHOOSER_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            var results: Array<Uri>? = null
            if (data == null || data.data == null) {
                if (cameraPhotoPath != null) {
                    results = arrayOf(Uri.parse(cameraPhotoPath))
                }
            } else {
                val dataString: String? = data.dataString
                if (dataString != null) {
                    results = arrayOf(Uri.parse(dataString))
                }
            }
            filePathCallback?.onReceiveValue(results)
            filePathCallback = null
        } else {
            filePathCallback?.onReceiveValue(null)
            filePathCallback = null
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        rxTimer.cancel()
        mViewParent?.removeAllViews()
        JCWebViewFactory.getInstance().recycleWebView(webView)
        super.onDestroy()
        com.niimbot.baselibrary.user.LoginDataEnum.unregisterChange(this)
    }

    var lastTime: Long = 0L
//    fun onBackPressed() {
//        if (hasGoBack()) {
//            webView?.goBack()
//        } else {
//            if (System.currentTimeMillis() - lastTime < 1000) {
//                activity?.finish()
//                return
//            }
//            lastTime = System.currentTimeMillis()
//        }
//    }

    fun onBackPressed(){
        if (canGoBack()) {
            webView?.loadUrl("javascript:navigateBack()")
        } else {
            webView?.goBack()
        }
    }

    data class StatusBean(var color: String = "#ffffff", var type: String = "1")

    private fun canGoBack() : Boolean{
        val originalUrl = webView?.copyBackForwardList()?.currentItem?.originalUrl
        if (originalUrl?.contains(ShopManager.SHOP_ROOT.replace("https://", "").replace("http://", "")) == true) {
           return  true
        }
        return  false
    }

    private fun hasGoBack() : Boolean{
        val originalUrl = webView?.copyBackForwardList()?.currentItem?.originalUrl
        if (webView?.canGoBack()!! && !(originalUrl.equals(url) || originalUrl.equals("$url/index"))) {
           return  true
        }
        return  false
    }


    /**
     * 更新商城数据
     */
    fun reloadStoreData(){
        LogUtils.d("reloadStoreData= $url, isVisible= $isVisible")
        if((!TextUtils.isEmpty(url) && "" != url)  && isVisible) {
            webView?.loadUrl(url)
        }
    }

    /**
     * 定时器控制商城fragment处于前台刷新
     */
    private val rxTimer =  RxTimer()
    fun exeRxTimer(){
        rxTimer.interval(1000 * 60 * 5, object :RxTimer.RxAction {
            override fun action(number: Long) {
               // reloadStoreData()
            }

            override fun onComplete() {
            }
        })
    }

    override fun loginStatusChange(isLogin: Boolean) {
        super.loginStatusChange(isLogin)
        LogUtils.d("loginStatusChange isLogin= $isLogin , isVisible = $isVisible")
        if (isLogin && mViewParent != null && !activity?.isFinishing!! && this.mView != null) {
            reInit()
//            if(!AppDataUtil.shopIsService){
                resetIndex()
//            }
            url += "&preloading=1"
            LogUtils.d("resetIndex preload= ", url)
            logE("url", "loginStatusChange: $url")
            webView?.loadUrl(url)
            initEvent(this.mView!!)
        }
    }

    fun appUserClickEvent(){
        shopLoadInitializeUtil?.appUserClickEvent()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: RefreshShopTokenEvent) {
        logI("wangxuhao","RefreshShopTokenEvent shoptoken=${HttpTokenUtils.getShopToken()}")
        val shopToken = HttpTokenUtils.getShopToken()
        val deviceId = BuriedHelper.getEventAnonymousId()
        val userId = LoginDataEnum.id.toString()
        val platform = "Android_YDY"
        webView?.evaluateJavascript("javascript:AppSetToken('${shopToken}', '${deviceId}', '${userId}', '${platform}')", null)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: AppLifecycleEvent) {
        val background = event.background
        if(background) {
            webView?.evaluateJavascript("javascript:applicationDidEnterBackground()", null)
        }
        else {
            webView?.evaluateJavascript("javascript:applicationWillEnterForeground()", null)
        }
    }

    override fun onDetach() {
        super.onDetach()
        EventBus.getDefault().unregister(this)
    }

    override val hostContext: Context
        get() = requireContext()

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        super.startActivityForResult(intent, requestCode)
    }

    override fun getFileProviderAuthority(): String = "com.gengcon.android.jccloudprinter.fileProvider"

    override fun createImageFile(): File? = try {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        requireActivity().getExternalFilesDir(null)?.let {
            File.createTempFile(imageFileName, ".jpg", it)
        }
    } catch (e: IOException) { null }

    override fun createVideoFile(): File? = try {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val videoFileName = "MP4_" + timeStamp + "_"
        requireActivity().getExternalFilesDir(null)?.let {
            File.createTempFile(videoFileName, ".mp4", it)
        }
    } catch (e: IOException) { null }

    override fun hostActivity(): Activity = requireActivity()
}
