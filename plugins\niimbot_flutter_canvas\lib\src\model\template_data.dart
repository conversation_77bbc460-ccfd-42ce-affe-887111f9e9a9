import 'dart:async';
import 'dart:convert' hide Codec;
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/config/element_create_required_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/plugin/goods_model.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/PrintModule.dart';
import 'package:niimbot_flutter_canvas/src/model/TemplateInputArea.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/date_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/stack_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/template_task.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/rfid_bind/rfid_info_manager.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

import '/src/widgets/attribute_panel/comm/font_file_manager.dart';
import 'element/serial_element.dart';

Logger _logger = Logger("TemplateData", on: kDebugMode);

/// 1.excel 列绑定信息刷新，将原有的 value "${0👁2}" 转换到 isBinding = 1  bindingColumn = 2
/// int isBinding;
/// int bindingColumn;

/// 2.固定的镜像 id 值，将原有的镜像 id 刷新到新的规则
/// String generateFixedMirrorId() {
///   return id.split('').reversed.join();
/// }

/// 3. 新增 dataBindingMode 和 goodsIds 字段
/// 外部数据模式, 参见 [DataBindingMode]
/// excel: Excel 导入
/// goods: 商品库导入
/// String dataBindingMode;
/// 已导入的商品列表
/// String goodsIds;

/// 数据绑定模式
/// excel: Excel 导入
/// goods: 商品库导入
class DataBindingMode {
  static const String none = '';
  static const String excel = 'excel';
  static const String commodity = 'commodity';
}

class TemplateData {
  ///模板id
  String id;

  ///模版相似度
  String similarity;

  ///云模板id
  String? cloudTemplateId;

  ///模板名称
  String name;

  ///模板多语言名称集合
  List<LabelNameInfo>? names;

  ///标签多语言名称集合
  List<LabelNameInfo> labelNames;

  ///本地缩略图地址
  String? localThumbnail;

  ///模板描述
  String? description;

  ///缩略图url
  String thumbnail;

  ///背景图url
  String backgroundImage;

  ///多背景图时的选择索引
  int multipleBackIndex;

  ///模板宽度
  num? width;

  ///模板高度
  num? height;

  ///出纸方向
  num rotate;

  ///耗材类型，1-标签类，2-标牌类
  num consumableType;

  ///纸张类型， 1-间隙纸，2-黑标纸，3-连续纸，4-定孔纸，5-透明纸
  num paperType;

  ///是否是线缆标签，0-非线缆标签，1-线缆标签
  bool isCable;

  ///尾巴长度（耗材类型为标签类，并且为线缆标签时处理该数据）
  num cableLength;

  ///尾巴方向，0-上，1-右，2-下，3-左
  num? cableDirection;

  ///模板上右下左边距
  List<double> margin;

  ///模板使用到的字体集合
  Map<String, dynamic>? usedFonts;

  ///模板的元素集合
  List<JsonElement> elements;

  Profile profile;

  ///标签纸id
  String? labelId = "";

  /// 附加数据：excel 数据源
  ExternalData? externalData;

  ///导入Excel的总页码
  int totalPage;

  /// 辅助变量：excel/商品库 数据绑定场景下 当前页 index, 0 开始
  int currentPageIndex;

  ///本地存储状态
  num local_type;

  ///模板或者标签携带的打印模式属性
  num templatePrintMode;

  ///模板来源，创建和更新时由服务端负责更新，本地只接收不做修改
  String platformCode;

  ///模板精度 默认打印机点数为203
  num paccuracyName;

  ///是否包含VIP资源
  bool hasVIPRes;

  ///是否VIP模板
  bool vip;

  ///是否商品模板
  bool commodityTemplate;

  String? version;

  bool isPrintHistory = false;

  ///画板旋转需求新增字段
  ///标明画板是否经过旋转，如果旋转过，需要手动修改画板背景和预览背景显示方向
  int canvasRotate;

  ///原模板的id:另存为或从云模板创建
  String? originTemplateId;

  ///市场运营需求字段，标明是否用户模板可转化为云模板*/
  num isEdited;

  ///模板前景图片*/
  String contentThumbnail;

  String localContentThumb;

  String localThumb;

  String layoutSchema;

  List<String> paperColor;

  List<String> localBackground;
  List<String> supportedEditors;

  Map<String, dynamic>? business;

  /// 数据绑定模式, 参见 [ExternalDataMode]
  /// excel: Excel 导入
  /// goods: 商品库导入
  String? dataBindingMode;

  /// 已导入的商品列表
  /// 用户打开已保存的模板时重新拉取最新的商品详情
  /// 需处理已经删除的商品
  String? goodsIds;

  /// 绑定的商品列表
  List<GoodsModel>? goodsData;

  /// excel 值修改记录
  TemplateTask? task;

  ///excel导入新结构 相关字段 excel修改
  TemplateModify? modify;

  ///excel导入新结构 相关字段 excel数据源
  List<DataSource>? dataSource;

  ExcelPageInfo? bindInfo;

  List<TemplateInputArea>? inputAreas;

  String? templateVersion;

  String? consumableTypeTextId;
  int printPaper = 0;

//是否被选中
  bool isSelected = false;

  List<CanvasElement> canvasElements = [];

  double? get designWidth => width?.mm2dp().toDouble();

  double? get designHeight => height?.mm2dp().toDouble();

  ///为了兼容老模板中元素id重复的问题
  List<String> elementIds = [];

  List<dynamic> printedProcessList;

  bool get isExcel =>
      (externalData?.externalDataList?.isNotEmpty ?? false) ||
      (dataSource != null && dataSource!.isNotEmpty && dataSource![0].type == DataSourceType.excel);

  @override
  int get hashCode => id.hashCode;

  /// 纸张类型， 1:间隙纸 2:黑标纸 3:连续纸 4:定孔纸 5:透明纸 6 标牌 10 黑标间隙纸 11 热缩管
  String get pageTypeDes {
    switch (paperType) {
      case 1:
        return intlanguage('app00280', '间隙纸');
      case 2:
        return intlanguage('app00283', '黑标纸');
      case 3:
        return intlanguage('app00282', '连续纸');
      case 4:
        return intlanguage('app00281', '定孔纸');
      case 5:
        return intlanguage('app00282', '透明纸');
      case 6:
        return intlanguage('app01231', '标牌');
      case 10:
        return intlanguage('app100000195', '黑标间隙');
      case 11:
        return intlanguage('app100000744', '热缩管');
      default:
        return '';
    }
  }

  TemplateData({
    this.id = '',
    this.name = '',
    this.cloudTemplateId,
    this.names,
    this.labelNames = const [],
    this.description,
    this.thumbnail = '',
    this.backgroundImage = '',
    this.multipleBackIndex = 0,
    this.width = 50.0,
    this.height = 30.0,
    this.rotate = 0,
    this.consumableType = 1,
    this.paperType = 1,
    this.isCable = false,
    this.cableLength = 0,
    this.cableDirection = 0,
    this.margin = const [0.0, 0.0, 0.0, 0.0],
    this.usedFonts,
    this.elements = const [],
    this.labelId,
    this.externalData,
    this.task,
    this.totalPage = 1,
    this.local_type = 0,
    this.localBackground = const [],
    this.supportedEditors = const [],
    this.templatePrintMode = -1,
    this.platformCode = "",
    this.paccuracyName = 203,
    this.hasVIPRes = false,
    this.vip = false,
    this.commodityTemplate = false,
    this.version,
    this.canvasRotate = 0,
    this.originTemplateId,
    this.isEdited = 0,
    this.contentThumbnail = "",
    this.localContentThumb = "",
    this.localThumb = "",
    this.paperColor = const ["0.0.0", "230.0.18"],
    this.canvasElements = const [],
    this.business,
    this.currentPageIndex = 0,
    this.dataSource,
    this.modify,
    this.bindInfo,
    this.templateVersion = '',
    this.layoutSchema = "",
    this.consumableTypeTextId,
    this.goodsData,
    Profile? profile,
    this.dataBindingMode,
    this.isPrintHistory = false,
    this.printedProcessList = const [],
    this.similarity = '0',
    this.inputAreas,
  }) : profile = profile ?? Profile();

  factory TemplateData.fromJson(Map<String, dynamic> json) {
    // final elements = (((json['elements'] as List?)
    //       ?..removeWhere((v) => v == null)
    //       ..map((e) => JsonElement.build(Map<String, dynamic>.from(e)))
    //           .toList()) as List<JsonElement>?) ??
    //     [];
    List<JsonElement>? elements = [];
    List datas = json['elements'] as List? ?? [];
    for (var item in datas) {
      if (item != null) {
        var element = JsonElement.build(Map<String, dynamic>.from(item));
        ///// 2025/1/6 Ice_Liu 兼容旧版数据imageUrl为空的异常
        if (element.itemType == ElementItemType.image &&
            ((element as ImageElement).imageUrl.isEmpty && element.localUrl.isEmpty)) {
          element.imageUrl =
              'https://oss-print.niimbot.com/public_resources/labels/a55dfc13cf7466a4782391cf41ef2fb8.png';
        }
        elements.add(JsonElement.build(Map<String, dynamic>.from(item)));
      }
    }
    List<String> elementIds = [];
    elements.forEach((element) {
      //边框元素需要刷新拉伸的参数 to check allowFreeZoom每次都会被刷新 没法保存
      if (element.isMaterialBoder()) {
        ImageElement e = element as ImageElement;
        e.allowFreeZoom = true;
      }

      /// 字段兼容处理
      /// 格式化镜像 id
      if (element.isOpenMirror == 1) {
        String newMirrorId = element.generateFixedMirrorId();
        final mirrorElement = elements.singleWhereOrNull((mirrorElement) => mirrorElement.id == element.mirrorId);
        if (mirrorElement != null) {
          element.mirrorId = newMirrorId;
          mirrorElement.id = newMirrorId;
        }
      }
      if (element is DateElement && !element.associated) {
        int associateType = !DateElementHelper.isSelectedByCommonlyUsedTime(
                DateElementHelper.getCommonlyUsedTimes(), element.validityPeriodUnit, element.validityPeriodNew)
            ? 1
            : 0;
        element.associateType = associateType;
      }

      ///兼容元素id重复的问题
      if (elementIds.contains(element.id)) {
        element.id = JsonElement.generateId();
      } else {
        elementIds.add(element.id);
      }
    });
    var currentPageIndex;

    /// 字段兼容处理
    /// 旧标记 currentPage 从 1 开始
    /// 新标记 currentPageIndex 从 0 开始, 便于数组操作
    if (json['currentPage'] is num && json['currentPage'] > 0) {
      currentPageIndex = json['currentPage'] - 1;
    } else {
      currentPageIndex = json['currentPageIndex'] ?? 0;
    }
    final externalData =
        json['externalData'] == null ? null : ExternalData.fromJson(Map<String, dynamic>.from(json['externalData']));

    final dataSource = (json['dataSource'] == null || json['dataSource'] == "" || (json['dataSource'] as List).isEmpty)
        ? (json['dataSources'] == null || json['dataSources'] == "" || (json['dataSources'] as List).isEmpty)
            ? null
            : [DataSource.fromJson(Map<String, dynamic>.from((json['dataSources'] as List).first))]
        : [DataSource.fromJson(Map<String, dynamic>.from((json['dataSource'] as List).first))];
    var modify;
    if (json['modify'] == null || (Map<String, dynamic>.from(json['modify'])).isEmpty) {
      modify = null;
    } else {
      Map<String, Map<String, DataBindModify>> result =
          TemplateModifyExtension.fromJson(Map<String, dynamic>.from(json['modify']));
      modify = result;
    }
    final totalPage = json['totalPage'] ?? 1;
    var bindInfo =
        json['bindInfo'] == null ? null : ExcelPageInfo.fromJson(Map<String, dynamic>.from(json['bindInfo']));
    // 存在dataSource但是不存在bindInfo,重新赋予新值
    if (dataSource != null && bindInfo?.page == null) {
      bindInfo = ExcelPageInfo(page: currentPageIndex, total: totalPage);
    }

    // 两者数据不统一，取bindInfo.page作为最终值
    // 仅仅处理currentPage的问题，新画板默认currentPageIndex和bindInfo.page一致
    if (bindInfo != null &&
        bindInfo.page != null &&
        currentPageIndex != bindInfo.page &&
        (json['currentPage'] is num &&
            json['currentPage'] > 0 &&
            ((externalData != null && (externalData.externalDataList?.isNotEmpty ?? false)) ||
                (dataSource?.isNotEmpty ?? false)))) {
      currentPageIndex = bindInfo.page! - 1;
    }
    final List<GoodsModel>? goodsData;
    List? goodsDataJsonList = (json['goodsData'] as List?)?..removeWhere((v) => v == null);
    if (goodsDataJsonList == null) {
      goodsData = null;
    } else if (goodsDataJsonList.isEmpty) {
      goodsData = [];
    } else {
      goodsData = goodsDataJsonList.map((e) => GoodsModel.fromJson(Map<String, dynamic>.from(e))).toList();
    }

    /// 转化为画板元素对象
    Iterable<CanvasElement> elementsList = [];
    elementsList = elements.map((e) => e.toCanvasElement());
    final canvasElements = elements.map((e) => e.toCanvasElement()).toList() ?? [];

    var dataBindingMode;

    final count = canvasElements.where((element) => (element.data.fieldName ?? '').length > 0).toList().length;

    /// 字段兼容处理
    /// 初始化 dataBindingMode 字段
    if (count > 0) {
      dataBindingMode = DataBindingMode.commodity;
    } else if ((externalData?.externalDataList?.isNotEmpty ?? false) &&
        (externalData?.externalDataList?.first.data?.columns.isNotEmpty ?? false) &&
        ((externalData?.externalDataList?.first.data?.columns.first.length ?? 0) > 0)) {
      dataBindingMode = DataBindingMode.excel;
    }

    // if (externalData?.externalDataList != null && externalData.externalDataList.isNotEmpty) {
    //   dataBindingMode = DataBindingMode.excel;
    // } else {
    //   dataBindingMode = DataBindingMode.none;
    // }
    var printedProcessList = [];
    if (json['printedProcessList'] != null) {
      printedProcessList = [];
      json['printedProcessList'].forEach((v) {
        printedProcessList.add(v);
      });
    }

    if (dataSource != null && dataSource.isNotEmpty) {
      dataBindingMode = dataSource.first.type.getStringValue();
    } else {
      dataBindingMode = DataBindingMode.none;
    }
    var usedFonts = (json['usedFonts'] == null)
        ? {"ZT001": "ZT001.ttf"}
        : (Map<String, dynamic>.from(json['usedFonts'])).isEmpty
            ? {"ZT001": "ZT001.ttf"}
            : Map<String, dynamic>.from(json['usedFonts']);
    if (!(usedFonts.containsKey(FontManager.FONT_DEFAULT_KEY))) {
      final languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
      if (languageType == "ar") {
        usedFonts[FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_ARIAL_LOCAL_FILE_NAME;
      } else if (languageType == "ja") {
        usedFonts[FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_JAPANESE_LOCAL_FILE_NAME;
      } else {
        usedFonts[FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_LOCAL_FILE_NAME;
      }
    }
    String similarity = json['similarity'] ?? '0';
    if (similarity.contains('.')) {
      similarity = (json['similarity'] as double).toStringAsFixed(2);
    }
    bool hasVIPResValue = false;
    final hasVipRes = json['hasVipRes'];
    final hasVIPRes = json['hasVIPRes'];
    if (hasVipRes != null || hasVIPRes != null) {
      final value = hasVipRes ?? hasVIPRes;
      hasVIPResValue = value is bool ? value : value == 1;
    }
    return TemplateData(
        id: json['id'] == null ? "" : json['id'].toString(),
        similarity: similarity,
        name: json['name'] ?? "",
        cloudTemplateId: json['cloudTemplateId'] ?? "",
        names: (json['names'] as List<dynamic>?)
                ?.map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        labelNames: (json['labelNames'] as List<dynamic>?)
                ?.map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        description: json['description'] ?? "",
        thumbnail: json['thumbnail'] ?? "",
        backgroundImage: json['backgroundImage'] ?? "",
        multipleBackIndex: json['multipleBackIndex'] ?? 0,
        layoutSchema: json['layoutSchema'] ?? "",
        height: json['height'],
        width: json['width'],
        rotate: json['rotate'] ?? 0,
        canvasElements: canvasElements,
        consumableType: json['consumableType'] ?? -1,
        paperType: json['paperType'] ?? 0,
        isCable: (json['isCable'] is bool) ? json['isCable'] : json['isCable'] == 1,
        cableDirection: json['cableDirection'],
        cableLength: json['cableLength'] ?? 0.0,
        margin: json['margin'] == null
            ? [0, 0, 0, 0]
            : (json['margin'] as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
        profile: json['profile'] == null ? Profile() : Profile.fromJson(Map<String, dynamic>.from(json['profile'])),
        labelId: json['labelId'] == null ? null : json['labelId'] as String,
        local_type: Platform.isIOS
            ? (json['localType'] == null
                ? (json['local_type'] == null ? 0 : json['local_type'] as num)
                : json['localType'] as num)
            : (json['local_type'] == null ? 0 : json['local_type'] as num),
        platformCode: json['platformCode'] == null ? "" : json['platformCode'],
        paccuracyName: json['paccuracyName'] ?? 0,
        commodityTemplate:
            (json['commodityTemplate'] is bool) ? json['commodityTemplate'] : json['commodityTemplate'] == 1,
        canvasRotate: json['canvasRotate'] ?? 0,
        originTemplateId: json['originTemplateId'] == null ? "" : json['originTemplateId'].toString(),
        contentThumbnail: json['contentThumbnail'] == null ? "" : json['contentThumbnail'] as String,
        localContentThumb: json['localContentThumb'] == null ? "" : json['localContentThumb'] as String,
        version: json['version'],
        paperColor: List<String>.from((json['paperColor'] ?? ["0.0.0", "230.0.18"])),
        elements: elements,
        business: json['business'],
        totalPage: totalPage,
        localBackground: (json['localBackground'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
        supportedEditors: (json['supportedEditors'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
        usedFonts: usedFonts,
        externalData: externalData,
        task: json['task'] == null ? null : TemplateTask.fromJson(Map<String, dynamic>.from(json['task'])),
        goodsData: goodsData,
        hasVIPRes: hasVIPResValue,
        vip: (json['vip'] == null)
            ? false
            : (json['vip'] is bool)
                ? json['vip']
                : json['vip'] == 1,
        isEdited: json['isEdited'] is String ? (num.tryParse(json['isEdited']) ?? 0) : json['isEdited'] ?? 0,
        consumableTypeTextId: json["consumableTypeTextId"],
        currentPageIndex: currentPageIndex,
        dataBindingMode: dataBindingMode,
        templateVersion: json['templateVersion'] ?? '',
        dataSource: dataSource,
        bindInfo: bindInfo,
        modify: modify,
        printedProcessList: printedProcessList,
        inputAreas: (json['inputAreas'] as List<dynamic>?)
            ?.map((e) => TemplateInputArea.fromJson(Map<String, dynamic>.from(e)))
            .toList())
      ..elementIds = elementIds;
  }

  TemplateData transformJson(Map<String, dynamic> json) {
    var templateData = TemplateData.fromJson(json);

    /// 字段兼容处理
    /// 旧标记 currentPage 从 1 开始
    /// 新标记 currentPageIndex 从 0 开始, 便于数组操作
    if (json['currentPage'] is num && json['currentPage'] > 0) {
      templateData.currentPageIndex = json['currentPage'] - 1;
    } else {
      templateData.currentPageIndex = json['currentPageIndex'] ?? 0;
    }

    templateData.totalPage = json['totalPage'] ?? 1;
    templateData.externalData =
        json['externalData'] == null ? null : ExternalData.fromJson(Map<String, dynamic>.from(json['externalData']));
    templateData.task = json['task'] == null ? null : TemplateTask.fromJson(Map<String, dynamic>.from(json['task']));
    templateData.dataSource =
        (json['dataSource'] == null || json['dataSource'] == "" || (json['dataSource'] as List).isEmpty)
            ? null
            : [DataSource.fromJson(Map<String, dynamic>.from((json['dataSource'] as List).first))];
    return templateData;
  }

  /// 获取打印 json string
  /// 元素 value 值转义, 处理内容包括:
  /// 1. excel、商品绑定的字段解析到对应文本值, 批量页数传入 [pageIndex]
  /// 2. 序列号按配置的规则生成内容
  /// 3. 日期按配置的规则格式化
  String generatePrintJson({int pageIndex = 0, bool savePrintTemplate = false, bool parseContentAffix = false}) {
    var elementCanvasDataJsonStr = jsonEncode(this.toJson(
        escapeValue: true,
        escapeBindingMethod: (JsonElement jsonElement) {
          return this.escapeBindingValue(jsonElement, pageIndex, parseContentAffix: parseContentAffix);
        },
        savePrintTemplate: savePrintTemplate));
    return elementCanvasDataJsonStr;
  }

  String generateColumnPreviewJson(
      {int pageIndex = 0,
      bool savePrintTemplate = false,
      bool parseContentAffix = false,
      required Function escapeFunc}) {
    var elementCanvasDataJsonStr = jsonEncode(this.toJson(
        escapeValue: true,
        escapeBindingMethod: (JsonElement jsonElement) {
          return escapeFunc(jsonElement, pageIndex, parseContentAffix: parseContentAffix);
        },
        savePrintTemplate: savePrintTemplate));
    return elementCanvasDataJsonStr;
  }

  Future<String> generatePrintJsonAsync(int pageIndex, bool savePrintTemplate, bool parseContentAffix) async {
    for (var element in this.canvasElements) {
      if (element.elementType == "image") {
        ImageElement imageElement = element.data as ImageElement;
        List<String> imagePaths = PdfBindInfoManager.instance.getPDFImagePathsWithElementId(imageElement.id);
        if (imagePaths.isNotEmpty) {
          //image数组 匹配 如果超出 则图片不显示
          imageElement.localUrl = pageIndex > imagePaths.length - 1 ? "" : imagePaths[pageIndex];
        }
      }
    }
    Map<String, dynamic> elementCanvasDataJsonStr = this.toJson(
        escapeValue: true,
        escapeBindingMethod: (JsonElement jsonElement) {
          return this.escapeBindingValue(jsonElement, pageIndex, parseContentAffix: parseContentAffix);
        },
        savePrintTemplate: savePrintTemplate,
        isShowSlashBg: true);
    return await IsolateUtil().invoke<String, Map<String, dynamic>>(jsonEncode, elementCanvasDataJsonStr);
  }

  /// 画板 json
  /// 元素 value 值未转义, 区别于 [generatePrintJson]
  String generateCanvasJson({bool savePrintTemplate = false, isNeedCorrectCurrentPage = false}) {
    if (isNeedCorrectCurrentPage == true) {
      this.currentPageIndex = 0;
      changePageInfo();
    }
    var elementCanvasDataJsonStr = jsonEncode(this.toJson(savePrintTemplate: savePrintTemplate));
    return elementCanvasDataJsonStr;
  }

  /// 转换为 json map
  /// [escapeValue] 字段是否转义
  /// [escapeBindingMethod] 字段转义的方法, 由 [TemplateData] 类按指定的 [currentPageIndex] 处理
  Map<String, dynamic> toJson(
      {bool escapeValue = false,
      EscapeBindingMethod? escapeBindingMethod,
      bool savePrintTemplate = false,
      bool isShowSlashBg = false,
      bool compactOldTemplate = false,
      bool handleTemplateType = true}) {
    /// 设置需转义标记
    if (escapeValue == true) {
      canvasElements.forEach((canvasElement) {
        canvasElement.data.escapeValue = true;
        canvasElement.data.escapeBindingMethod = escapeBindingMethod;
      });
    }
    canvasElements.forEach((canvasElement) {
      canvasElement.data.compactOldTemplate = compactOldTemplate;
    });
    // _updateResourceVersion();
    //处理商品模版标识
    if (handleTemplateType) {
      if (dataSource?.isNotEmpty ?? false) {
        if (dataSource!.first.type == DataSourceType.commodity) {
          profile.extrain.templateType = 2;
          commodityTemplate = true;
          task = null;
          if (dataSource!.first.params != null &&
              dataSource!.first.params["commodity"] != null &&
              dataSource!.first.params["ids"] == null) {
            Map commodityInfo = dataSource!.first.params["commodity"];
            if (commodityInfo["id"] != null && (commodityInfo["id"] as String).isNotEmpty) {
              dataSource!.first.params = {
                "ids": [commodityInfo["id"]]
              };
            }
          }
        } else {
          profile.extrain.goodsCode = "";
          if (profile.extrain.templateType == 2) {
            //为了解决打印记录-1过来重新赋值导致的模板上传失败的情况
            profile.extrain.templateType = 0;
          }
          commodityTemplate = false;
        }
      } else {
        profile.extrain?.goodsCode = "";
        if (profile.extrain?.templateType == 2) {
          //为了解决打印记录-1过来重新赋值导致的模板上传失败的情况
          profile.extrain?.templateType = 0;
        }
        commodityTemplate = false;
      }
    }

    var result = <String, dynamic>{
      'id': id ?? '',
      'cloudTemplateId': cloudTemplateId ?? '',
      'name': name ?? '',
      'names': names?.map((e) => e.toJson()).toList() ?? [],
      'labelNames': labelNames.map((e) => e.toJson()).toList() ?? [],
      'description': description,
      'thumbnail': thumbnail ?? '',
      'backgroundImage': backgroundImage ?? '',
      'multipleBackIndex': multipleBackIndex ?? 0,
      'width': width?.digits(2),
      'height': height?.digits(2),
      'rotate': rotate,
      'consumableType': consumableType,
      'paperType': paperType ?? 0,
      'isCable': isCable,
      'layoutSchema': layoutSchema,
      'cableLength': cableLength ?? 0,
      'cableDirection': cableDirection ?? 0,
      'margin': margin ?? [0, 0, 0, 0],
      'usedFonts': usedFonts ?? {"ZT001": "ZT001.ttf"},
      'elements': getElements()
              .map((e) => savePrintTemplate ? e.antiEscapeValueToJson() : setSlashBgElementJson(e, isShowSlashBg))
              .toList() ??
          [],
      'profile': profile.toJson(),
      'labelId': labelId ?? '',
      'currentPageIndex': currentPageIndex ?? 0,
      'currentPage': getCurrentPage(),
      'totalPage': totalPage ?? 1,
      'templatePrintMode': templatePrintMode,
      'platformCode': platformCode,
      'paccuracyName': paccuracyName,
      'commodityTemplate': commodityTemplate ?? false,
      'contentThumbnail': contentThumbnail ?? '',
      'localContentThumb': localContentThumb ?? '',
      'local_thumb': localThumb ?? '',
      'version': version ?? '',
      'originTemplateId': originTemplateId ?? '',
      'canvasRotate': canvasRotate,
      'business': business,
      'localBackground': localBackground ?? [],
      'externalData': externalData?.toJson(),
      'task': task?.toJson(),
      'dataSource': (dataSource?.isEmpty ?? true) ? null : [dataSource?.first.toJson()],
      'modify': modify?.toJson(),
      'bindInfo': bindInfo?.toJson(),
      'templateVersion': templateVersion,
      'consumableTypeTextId': consumableTypeTextId,
      'paperColor': paperColor,
      'vip': vip,
      'supportedEditors': supportedEditors ?? [],
      'hasVIPRes': hasVIPRes,
      'hasVipRes': hasVIPRes,
      'isEdited': isEdited,
      "local_type": local_type,
      "localType": local_type,
      "inputAreas": inputAreas?.map((e) => e.toJson()).toList() ?? []
      // 'printedProcessList': printedProcessList?.map((v) => v.toJson()).toList()
    };

    if (externalData == null) result.remove("externalData");
    if (task == null) result.remove("task");

    /// 还原转义标识标记
    canvasElements.forEach((canvasElement) {
      canvasElement.data.escapeValue = false;
      canvasElement.data.escapeBindingMethod = null;
      canvasElement.data.compactOldTemplate = false;
    });
    // if (savePrintTemplate && task != null && task.modifyData != null && task.modifyData.isNotEmpty) {
    //   TemplateTask savePrintTemplateTask = _getSavePrintTemplateTask();
    //   result["task"] = savePrintTemplateTask.toJson();
    // }
    // if (savePrintTemplate) {
    //   if( modify != null && modify.isNotEmpty){
    //     TemplateModify savePrintTemplateTask = _getSavePrintTemplateTask();
    //     result["modify"] = savePrintTemplateTask.toJson();
    //     result["task"] = ExcelTransformManager().transformTemplateModifyToTemplateTask().toJson();
    //   }
    //   if(dataSource != null && dataSource.isNotEmpty){
    //     result["externalData"] = ExcelTransformManager().transformDataSourceToExternalData().toJson();
    //   }
    // }
    if (savePrintTemplate) {
      if (modify?.isNotEmpty ?? false) {
        final savePrintTemplateTask = _getSavePrintTemplateTask();
        result["modify"] = savePrintTemplateTask?.toJson();
      }
    }
    return result;
  }

  //预览场景 绑定元素显示斜线背景
  Map<String, dynamic> setSlashBgElementJson(JsonElement e, bool isShowSlashBg) {
    // isShowSlashBg = true;
    Map<String, dynamic> elementJson = e.toJson();
    if (!isShowSlashBg) {
      return elementJson;
    }
    if (e is! TableElement) {
      if ((e.dataBind ?? []).isNotEmpty) {
        elementJson["slashBgMode"] = 1;
      }
    } else {
      //遍历表格单元格 设置斜线
      List<Map<String, dynamic>> cells = elementJson["cells"] ?? [];
      cells = cells.map((element) {
        List dataBind = element["dataBind"] ?? [];
        if (dataBind.isNotEmpty) {
          element["slashBgMode"] = 1;
        }
        return element;
      }).toList();
      //遍历表格合并单元 设置斜线
      List<Map<String, dynamic>> combineCells = elementJson["combineCells"] ?? [];
      combineCells = combineCells.map((element) {
        List dataBind = element["dataBind"] ?? [];
        if (dataBind.isNotEmpty) {
          element["slashBgMode"] = 1;
        }
        return element;
      }).toList();
      elementJson["cells"] = cells;
      elementJson["combineCells"] = combineCells;
    }
    return elementJson;
  }

  TemplateModify? _getSavePrintTemplateTask() {
    final bindingJsonElements = getAllBindingExcelElements(false);
    // TemplateTask savePrintTemplateTask = task.clone();
    // Map<String, Map<String, String>> modifyData = savePrintTemplateTask.modifyData;
    final savePrintTemplateTask = cloneTemplateModify();
    Map<String, Map<String, DataBindModify>?>? modifyData = savePrintTemplateTask;
    modifyData?.removeWhere(
        (key, value) => !(bindingJsonElements?.any((element) => element.isExcelModifyIdMatch(key)) ?? false));
    List<String>? elementIds = modifyData?.keys.toList();
    elementIds?.forEach((elementId) {
      String? mirrorElementId = bindingJsonElements?.firstWhereOrNull((element) => element.id == elementId)?.mirrorId;
      if (mirrorElementId != null && mirrorElementId.isNotEmpty) {
        modifyData?[mirrorElementId] = modifyData[elementId];
      }
    });
    return savePrintTemplateTask;
  }

  _updateResourceVersion() {
    hasVIPRes = hasVipElement();
    // TODO: 2023/6/13 Ice_Liu 更新resourceVersion
  }

  bool needVip() {
    return hasVipElement() || vip;
  }

  bool hasVipElement() {
    bool hasVip = false;
    if ((canvasElements.length ?? 0) > 0) {
      hasVip = canvasElements.any((element) => element.data.hasVipSource());
    }
    return hasVip;
  }

  int getCurrentPage() {
    return currentPageIndex + 1;
  }

  String getSimpleInfo() {
    String info = intlanguage('app100000966', '自定义') + "-${width?.toDouble()}X${height?.toDouble()}";
    if (cableLength > 0) {
      info += "+$cableLength";
    }
    String paperTypeName = intlanguage('${getPaperTypeName()}', '走纸类型');
    info += "-$paperTypeName";
    String labelName = "";
    String labelEnName = "";
    String labelCNName = '';
    for (LabelNameInfo labelNameInfo in labelNames) {
      if (CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType() == labelNameInfo.languageCode) {
        labelName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "en") {
        labelEnName = labelNameInfo.name;
      }
      if (labelNameInfo.languageCode == "zh-cn") {
        labelCNName = labelNameInfo.name;
      }
    }
    // 当前语言> EN > CN
    labelName = labelName.isEmpty ? (labelEnName.isEmpty ? labelCNName : labelEnName) : labelName;
    if (profile.extrain.labelId?.isNotEmpty == true) {
      info = labelName;
    }
    return info;
  }

  ///重新设置元素关联
  resetCanvasElementAssociate(List<CanvasElement> undocanvasElements, bool isUndo,
      {List<JsonElement>? beforeElements}) {
    CanvasElement canvasElement = undocanvasElements.first;
    if (canvasElement.data is! DateElement) return;
    DateElement dateElement = canvasElement.data as DateElement;
    DateElement? dateElement1, dateElement2;
    if (dateElement.associateId.isEmpty) return;
    undocanvasElements.forEach((canvasElement) {
      DateElement dateElement = canvasElement.data as DateElement;
      if (dateElement.associated) {
        dateElement1 = dateElement;
      } else {
        dateElement2 = dateElement;
      }
      if (dateElement.isOpenMirror == 1) {
        canvasElement.mirrorNeedRefresh = true;
      }
    });
    //撤销时删除关联元素 还原原始元素、恢复时新增关联元素，设置原始元素关联属性
    if (isUndo) {
      int dateIsRefresh = 0;
      if (beforeElements?.isNotEmpty == true) {
        DateElement firstDateElement = beforeElements?.first as DateElement;
        dateIsRefresh = firstDateElement.dateIsRefresh;
      }
      for (var element in canvasElements) {
        if (element.data is DateElement) {
          DateElement dateElement = element.data as DateElement;
          if (dateElement.id == dateElement1?.id) {
            dateElement.associated = false;
            dateElement.dateIsRefresh = dateIsRefresh;
            dateElement.associateId = "";
          }
          if (dateElement.mirrorId == dateElement1?.id) {
            dateElement.associateId = "";
            dateElement.associated = false;
            dateElement.dateIsRefresh = dateIsRefresh;
          }
        }
      }
      canvasElements.removeWhere(
          (element) => (element.elementId == dateElement2?.id || element.data.mirrorId == dateElement2?.id));
    } else {
      for (var element in canvasElements) {
        if (element.data is DateElement) {
          DateElement dateElement = element.data as DateElement;
          if (dateElement.id == dateElement1?.id) {
            dateElement.associated = dateElement1!.associated;
            dateElement.dateIsRefresh = dateElement1!.dateIsRefresh;
            dateElement.associateId = dateElement1!.associateId;
          }
          if (element.data.isOpenMirror == 1) {
            element.mirrorNeedRefresh = true;
          }
        }
      }
      CanvasElement? associateElement = dateElement2!.clone(keepId: true).toCanvasElement();
      canvasElements.addAll([associateElement]);
    }
  }

  String getConsumableTypeString(String consumableType) {
    return CanvasPluginManager().nativeMethodImpl?.getConsumableName(consumableType) ?? 'app01230';
  }

  String? getPaperTypeName() {
    return CanvasPluginManager().nativeMethodImpl?.getPaperTypeName(paperType.toString());
  }

  /// 清空画板元素
  clearElements() {
    canvasElements.clear();
    elements.clear();
    // TODO: 2023/5/11 Ice_Liu 需要清空excel绑定，商品绑定等数据，是否退出画板时清理
  }

  /// 重置 elements 的图像缓存
  resetImageCache() {
    canvasElements.forEach((element) {
      element.resetImageCache();
    });
  }

  /// 还原 canvas element 到 json element
  List<JsonElement> getElements() {
    sortElements();
    return canvasElements.map((e) => e.data).toList() ?? [];
  }

  ///根据元素类型重新排序
  sortElements() {
    List<CanvasElement> bordersAndGraphs = [];
    //List<CanvasElement> graphs = [];
    List<CanvasElement> tableAndImages = [];
    List<CanvasElement> others = [];
    for (var element in canvasElements ?? []) {
      //图片类型的边框和形状放到最底层
      if ((element.elementType == "image" && element.data.isMaterialBoder()) || element.elementType == "graph") {
        bordersAndGraphs.add(element);
        //表单和图片
      } else if (element.elementType == "table" || element.elementType == "image") {
        tableAndImages.add(element);
      } else {
        others.add(element);
      }
    }
    canvasElements.clear();
    canvasElements.addAll(bordersAndGraphs);
    canvasElements.addAll(tableAndImages);
    canvasElements.addAll(others);
  }

  /// 是否为商品标签模板
  bool isGoodsLabelTemplate() {
    return dataBindingMode == DataBindingMode.commodity;
  }

  /// 是否为 Excel 绑定模板
  bool isExcelBindingTemplate() {
    return dataBindingMode == DataBindingMode.excel && (dataSource?.isNotEmpty ?? false);
  }

  /// 关联Excel的文件名
  /// [type] 0-全名包括扩展名，1-全名不包括扩展名，2-只获取扩展名
  String getBindExcelFileName({int type = 0}) {
    // if (externalData == null || externalData.externalDataList == null || externalData.externalDataList.isEmpty) {
    //   return "";
    // }
    // String fullName = externalData.fileName;
    if (!isExcelBindingTemplate()) {
      return "";
    }
    String? fullName = ExcelTransformManager().getDataSource()?.name;
    return ExcelManager.sharedInstance().getExcelDisplayNameByFullName(fullName, type: type);
  }

  /// 关联Excel元素被清除后，清空Excel数据
  clearImportExcelIfNecessary() {
    // if ((dataBindingMode == DataBindingMode.excel || externalData != null) && getBindExcelColumnCount() == 0) {
    if ((dataBindingMode == DataBindingMode.excel || dataSource != null) && getBindExcelColumnCount() == 0) {
      injectExcelData(null);
    }
  }

  /// 已绑定列数
  int getBindExcelColumnCount() {
    final bindingJsonElements =
        canvasElements.where((element) => element.data.isBindingExcel()).map((e) => e.data).toList();
    if (bindingJsonElements.isEmpty) {
      return 0;
    }
    List<int> columns = [];
    int count = 0;
    bindingJsonElements.forEach((item) {
      if (item is TableElement) {
        item.getBindingExcelCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
        item.getBindingExcelCombineCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
      } else {
        int column = item.bindingColumn;
        if (!columns.contains(column)) {
          count++;
          columns.add(column);
        }
      }
    });
    return count;
  }

  /// 已绑定列数
  int getBindGoodsDataCount() {
    final bindingJsonElements =
        canvasElements.where((element) => element.data.isBindingExcel()).map((e) => e.data).toList();
    if (bindingJsonElements.isEmpty) {
      return 0;
    }
    List<int> columns = [];
    int count = 0;
    bindingJsonElements.forEach((item) {
      if (item is TableElement) {
        item.getBindingExcelCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
        item.getBindingExcelCombineCells().forEach((cell) {
          int column = cell.bindingColumn;
          if (!columns.contains(column)) {
            count++;
            columns.add(column);
          }
        });
      } else {
        int column = item.bindingColumn;
        if (!columns.contains(column)) {
          count++;
          columns.add(column);
        }
      }
    });
    return count;
  }

  List<ExcelBindInfo> getBindList() {
    final bindingJsonElements =
        canvasElements.where((element) => element.data.isBindingExcel()).map((e) => e.data).toList();
    if (bindingJsonElements.isEmpty) {
      return [];
    }
    // List<String> columnHeaders =
    //     externalData?.externalDataList?.firstWhere((element) => true, orElse: () => null)?.data?.columnHeaders;
    List<String> columnHeaders = ExcelTransformManager().getExcelHeaders();
    if (columnHeaders.isEmpty) {
      return [];
    }
    List<ExcelBindInfo> bindList = [];
    if (dataSource?.first.type == DataSourceType.commodity) {
      columnHeaders = goodsInfnFieldDescName();
    }
    // int excelId = int.parse(externalData.id);
    DataSource? ds = ExcelTransformManager().getDataSource();
    String excelMd5 = ds?.hash ?? '';
    for (int column = 0; column < columnHeaders.length; column++) {
      for (int i = 0; i < bindingJsonElements.length; i++) {
        JsonElement item = bindingJsonElements[i];
        if (item is TextElement) {
          if (item.bindingColumn == column) {
            ExcelBindInfo excelBindInfo = ExcelBindInfo();
            // excelBindInfo.excelId = excelId;
            excelBindInfo.excelMd5 = excelMd5;
            excelBindInfo.column = column;
            excelBindInfo.select = true;
            bindList.add(excelBindInfo);
            break;
          }
        }
        if (item is TableElement) {
          bool find = false;
          item.getBindingExcelCells().forEach((cell) {
            if (!find && cell.bindingColumn == column) {
              ExcelBindInfo excelBindInfo = ExcelBindInfo();
              // excelBindInfo.excelId = excelId;
              excelBindInfo.excelMd5 = excelMd5;
              excelBindInfo.column = column;
              excelBindInfo.select = true;
              bindList.add(excelBindInfo);
              find = true;
            }
          });
          if (find) {
            break;
          }
          item.getBindingExcelCombineCells().forEach((cell) {
            if (!find && cell.bindingColumn == column) {
              ExcelBindInfo excelBindInfo = ExcelBindInfo();
              // excelBindInfo.excelId = excelId;
              excelBindInfo.excelMd5 = excelMd5;
              excelBindInfo.column = column;
              excelBindInfo.select = true;
              bindList.add(excelBindInfo);
              find = true;
            }
          });
          if (find) {
            break;
          }
        }
        if (item is BarCodeElement) {
          if (item.bindingColumn == column) {
            ExcelBindInfo excelBindInfo = ExcelBindInfo();
            // excelBindInfo.excelId = excelId;
            excelBindInfo.excelMd5 = excelMd5;
            excelBindInfo.column = column;
            excelBindInfo.bindType = ElementItemType.barcode;
            excelBindInfo.barcodeType = item.codeType;
            excelBindInfo.select = true;
            bindList.add(excelBindInfo);
            break;
          }
        }
        if (item is QrCodeElement) {
          if (item.bindingColumn == column) {
            ExcelBindInfo excelBindInfo = ExcelBindInfo();
            // excelBindInfo.excelId = excelId;
            excelBindInfo.excelMd5 = excelMd5;
            excelBindInfo.column = column;
            excelBindInfo.bindType = ElementItemType.qrcode;
            excelBindInfo.qrCodeType = item.codeType;
            excelBindInfo.select = true;
            bindList.add(excelBindInfo);
            break;
          }
        }
      }
    }
    return bindList;
  }

  bool isBindExcelWithHeader() {
    final bindingJsonElements =
        canvasElements.where((element) => element.data.isBindingExcel()).map((e) => e.data).toList();
    if (bindingJsonElements.isEmpty) {
      return false;
    }
    for (int i = 0; i < bindingJsonElements.length; i++) {
      JsonElement element = bindingJsonElements[i];
      if (element is TextElement) {
        // if (element.isTitle == true || element.contentTitle != null) {
        //   return true;
        // }
        DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(element);
        if (dataBindModify != null && dataBindModify.useTitle == true) {
          return true;
        }
      }
      if (element is TableElement) {
        List<TableCellElement> cells = element.getBindingExcelCells();
        for (int i = 0; i < cells.length; i++) {
          TableCellElement cell = cells[i];
          // if (cell.isTitle == true || cell.contentTitle != null) {
          //   return true;
          // }
          DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(cell);
          if (dataBindModify != null && dataBindModify.useTitle == true) {
            return true;
          }
        }
        ;
        List<TableCellElement> combineCells = element.getBindingExcelCombineCells();
        for (int i = 0; i < combineCells.length; i++) {
          TableCellElement cell = combineCells[i];
          // if (cell.isTitle == true || cell.contentTitle != null) {
          //   return true;
          // }
          DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(cell);
          if (dataBindModify != null && dataBindModify.useTitle == true) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /// 返回所有绑定Excel的元素集合
  /// [containMirror] 是否返回绑定Excel的镜像元素
  List<CanvasElement>? getAllBindingExcelCanvasElements(bool containMirror) {
    if (containMirror) {
      return canvasElements.where((element) => element.data.isBindingExcel()).toList();
    } else {
      return canvasElements
          .where((element) => element.data.isBindingExcel() && !element.data.isMirrorElement())
          .toList();
    }
  }

  List<JsonElement>? getAllBindingExcelElements(bool containMirror) {
    return getAllBindingExcelCanvasElements(containMirror)?.map((e) => e.data).toList();
  }

  /// 包含的商品字段
  List<String> goodsFields() {
    final fields = canvasElements.map((e) => e.data.fieldName ?? '').toSet();
    fields..remove('');
    return fields.toList() ?? [];
  }

  /// 存在批量的数据 (excel, 商品导入)
  bool existBatchBindingData() {
    if (isGoodsLabelTemplate()) {
      /// 商品导入
      return true;
    } else if (isExcelBindingTemplate()) {
      /// excel
      // if (externalData?.externalDataList == null || externalData.externalDataList.isEmpty) {
      //   return false;
      // } else {
      //   return /*(externalData?.externalDataList?.first?.data?.columns?.first?.length ?? 0) > 0 ||*/
      //       (externalData?.externalDataList?.first?.data?.columnHeaders?.length ?? 0) > 0;
      // }
      return true;
    }
    return false;
  }

  /// 批量数据的数量
  int pagesOfBatchData() {
    if (!existBatchBindingData()) {
      int pdfPages = PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(this);
      if (totalPage <= pdfPages) {
        return pdfPages;
      } else {
        return 0;
      }
    }
    if (isExcelBindingTemplate() || isGoodsLabelTemplate()) {
      /// excel
      // int length = externalData.externalDataList.first.data.columns.first.length;
      // int length = (totalPage != null && totalPage>1)?totalPage:ExcelTransformManager().getExcelRowCount()-1;
      // int length = ExcelTransformManager().rowData?.length ?? 0;
      // if (length == 0) {
      //   length = 1;
      // }
      // return length;
      if (totalPage != 0) {
        return totalPage;
      }
      return totalPage;
    }

    return 0;
  }

  /// 注入 商品库 数据
  bool injectGoodsData(List<GoodsModel>? goodsDataInject) {
    try {
      currentPageIndex = 0;
      final originGoodsData = goodsData;
      goodsData = goodsDataInject;

      /// 暂存变更记录
      StackManager().stashGoodsDataRecord(originGoodsData, goodsData);
      return true;
    } catch (_) {
      return false;
    }
  }

  /// 注入 excel 数据
  // bool injectExcelData(ExcelDataModel excelData) {
  //   try {
  //     ExternalData originExternalData = externalData;
  //     if (originExternalData == null || excelData == null || originExternalData.id != excelData.excelId) {
  //       currentPageIndex = 0;
  //     }
  //     // Map<String, dynamic> originTask = task;
  //     //
  //     // task = null;
  //     if (excelData == null) {
  //       externalData = null;
  //       dataBindingMode = DataBindingMode.none;
  //       totalPage = 1;
  //     } else {
  //       externalData = ExternalData(externalDataList: [
  //         ExternalListModel(
  //             name: excelData.fileName,
  //             data: ExternalListModelData(columnHeaders: excelData.headers, columns: excelData.columnData))
  //       ], id: excelData.excelId, fileName: excelData.fileName);
  //       dataBindingMode = DataBindingMode.excel;
  //       totalPage = excelData.columnData[0].length;
  //       if (totalPage == 0) {
  //         totalPage = 1;
  //       }
  //     }
  //
  //     /// 暂存变更记录
  //     // StackManager().stashExternalExcelDataRecord(originExternalData, externalData);
  //     // StackManager().stashTaskRecord(originTask, null);
  //     return true;
  //   } catch (_) {
  //     return false;
  //   }
  // }
  bool injectExcelData(ExcelDataModel? excelData) {
    try {
      resetImageCache();
      final originDataSource = dataSource;
      if (originDataSource == null || excelData == null || getExcelMd5() != excelData.excelId) {
        if (!((dataSource?.isNotEmpty ?? false) &&
                dataSource!.first.type == DataSourceType.commodity &&
                excelData != null &&
                dataSource!.first.type.getStringValue() == excelData.type) ||
            currentPageIndex > excelData.columnData.length - 1) {
          //判断元素PDF绑定，如果当前页大于pdf总页数，解除excel绑定后 当前页设置为pdf总页数
          int pdfEnablePrintMax = PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(this);
          if (pdfEnablePrintMax > 0) {
            currentPageIndex = currentPageIndex > pdfEnablePrintMax - 1 ? pdfEnablePrintMax - 1 : currentPageIndex;
          } else {
            currentPageIndex = 0;
          }
        }
      }
      // TemplateModify originModify = TemplateUtils.cloneModify(modify);
      // Map<String, dynamic> originTask = task;
      //
      // task = null;
      if (excelData == null) {
        dataSource = null;
        profile.extrain.templateType = 0;
        commodityTemplate = false;
        modify?.clear();
        dataBindingMode = DataBindingMode.none;
        totalPage = 1;
        bindInfo = null;
        RfidInfoManager().setRfidBindingColumn(null);
        //判断元素PDF绑定，当前元素绑定了pdf 解除excel绑定时 元素图缓存清除
        for (var element in canvasElements) {
          if (element.elementType == "image") {
            ImageElement imageElement = element.data as ImageElement;
            if (PdfBindInfoManager.instance.elementIsBindPdf(imageElement.id)) {
              element.imageCache = null;
            }
          }
        }
        injectExcelData(null);
      } else {
        // externalData = ExternalData(externalDataList: [
        //   ExternalListModel(
        //       name: excelData.fileName,
        //       data: ExternalListModelData(columnHeaders: excelData.headers, columns: excelData.columnData))
        // ], id: excelData.excelId, fileName: excelData.fileName);
        if (getExcelMd5() != excelData.excelId) {
          ExcelTransformManager().setDataSourceWrapper(ExcelManager().dataSourceWrapper);
          totalPage = ExcelTransformManager().getExcelRowCount(filterEmptyRow: true);
          if (totalPage == 0) {
            totalPage = 1;
            if (excelData.type == DataSourceType.commodity.getStringValue()) {
              currentPageIndex = 0;
            }
          }
        }
        if (totalPage > 0 && currentPageIndex >= totalPage) {
          currentPageIndex = totalPage - 1;
        }
        changePageInfo();
        dataSource = ExcelTransformManager().templateData?.dataSource;
        RfidInfoManager().setRfidBindingColumn(excelData.rfidBindColumn);
        for (var element in canvasElements) {
          if (PdfBindInfoManager.instance.elementIsBindPdf(element.elementId)) {
            element.imageCache = null;
          }
        }
        dataBindingMode = dataSource?.first.type.getStringValue();
        // totalPage = excelData.columnData[0].length;
      }

      /// 暂存变更记录
      // StackManager().stashExternalExcelDataRecord(originExternalData, externalData);
      // StackManager().stashTaskRecord(originTask, null);
      // StackManager().stashTemplateModifyRecord(originModify, TemplateUtils.cloneModify(modify));
      return true;
    } catch (_) {
      return false;
    }
  }

  injectDataSource() {
    ExcelTransformManager().setDataSourceWrapper(ExcelManager().dataSourceWrapper);
    totalPage = ExcelTransformManager().getExcelRowCount(filterEmptyRow: true);
    if (totalPage == 0) {
      totalPage = 1;
      currentPageIndex = 0;
    } //
    changePageInfo();
  }

  changePageInfo() {
    bindInfo = ExcelPageInfo(page: currentPageIndex + 1, total: totalPage);
  }

  bool isContaintInstantTime() {
    bool isInstantTime = false;
    for (var canvasElement in canvasElements) {
      JsonElement element = canvasElement.data;
      if (element is DateElement && element.dateIsRefresh == 1) {
        isInstantTime = true;
        break;
      }
    }
    return isInstantTime;
  }

  /// 清空 excel 列绑定关系
  List<CanvasElement> clearExcelColumnBinding() {
    List<CanvasElement> clearedCanvasElements = [];
    canvasElements.forEach((element) {
      if (element.data is TableElement) {
        bool flag = false;
        final tableElement = element.data as TableElement;
        tableElement.cells.forEach((e) {
          // if (e.isBinding == 1) {
          if (e.isBindingElement()) {
            flag = true;
            // e.isBinding = 0;
            e.bindingColumn = -1;
            e.dataBind = null;
            e.value = "";
          }
        });
        tableElement.combineCells.forEach((e) {
          // if (e.isBinding == 1) {
          if (e.isBindingElement()) {
            flag = true;
            // e.isBinding = 0;
            e.bindingColumn = -1;
            e.dataBind = null;
            e.value = "";
          }
        });
        if (flag) {
          clearedCanvasElements.add(element);
        }
      } else {
        // if (element.data.isBinding == 1) {
        if (element.data.isBindingElement()) {
          clearedCanvasElements.add(element);
          // element.data.isBinding = 0;
          element.data.bindingColumn = -1;
          element.data.dataBind = null;
          element.data.value = "";
        }
      }
    });
    return clearedCanvasElements;
  }

  /// 清空 excel 列绑定关系
  List<CanvasElement> clearGoodsFieldBinding() {
    List<CanvasElement> clearedCanvasElements = [];
    canvasElements.forEach((element) {
      if ((element.data.fieldName ?? '').isNotEmpty) {
        clearedCanvasElements.add(element);
        element.data.fieldName = '';
      }
    });
    return clearedCanvasElements;
  }

  /// 转义绑定列的内容
  String escapeBindingValue(JsonElement jsonElement, int? pageIndex,
      {bool addContentTitle = true, bool parseContentAffix = false}) {
    if (this.existBatchBindingData() && (jsonElement.dataBind?.isNotEmpty ?? false)) {
      try {
        if (isExcelBindingTemplate()) {
          /// excel 导入场景
          String escapeValue = '';
          // escapeValue =  NiimbotDataSourceUtils.getElementBindValueSync(jsonElement.id, jsonElement.value, jsonElement.dataBind, this.dataSource, modify, pageIndex+1,TemplateUtils.documentPath);

          escapeValue = ExcelTransformManager().getBindingValue(
              jsonElement, pageIndex, addContentTitle && jsonElement is TextElement, parseContentAffix);
          if (jsonElement is BarCodeElement && jsonElement.codeType == BarcodeType.CODE_BAR) {
            if ((escapeValue?.length ?? 0) > 57) {
              escapeValue = escapeValue.substring(0, 57);
            }
          }
          return escapeValue;
        } else if (isGoodsLabelTemplate()) {
          /// 商品导入场景
          String escapeValue = "";
          escapeValue = ExcelTransformManager().getBindingValue(
              jsonElement, pageIndex, addContentTitle && jsonElement is TextElement, parseContentAffix);
          return escapeValue;
        }
      } catch (_) {
        return jsonElement.value!;
      }
    } else if (jsonElement is SerialElement) {
      SerialElement serialElement = jsonElement;
      String content =
          ((int.tryParse(serialElement.startNumber ?? '1') ?? 0) + serialElement.incrementValue * (pageIndex ?? 0))
              .toString()
              .padLeft((serialElement.startNumber ?? '').length, '0');
      String value = '${serialElement.prefix ?? ''}$content${serialElement.suffix ?? ''}';
      return value;
    }
    return jsonElement.value!;
  }

  // String escapeBindingValue(JsonElement jsonElement, int pageIndex, {bool addContentTitle = true}) {
  //   if (this.existBatchBindingData() && (jsonElement.isBinding == 1 || (jsonElement.fieldName ?? '').isNotEmpty)) {
  //     try {
  //       if (isExcelBindingTemplate() && jsonElement.isBinding == 1) {
  //         /// excel 导入场景
  //         String escapeValue = '';
  //
  //         /// 查询 excel 修改记录
  //         String findModifyValue = null;
  //         if (jsonElement.isMirrorElement()) {
  //           findModifyValue = task?.getModifyValue(jsonElement.mirrorId, pageIndex);
  //         } else {
  //           findModifyValue = task?.getModifyValue(jsonElement.id, pageIndex);
  //         }
  //         if (findModifyValue != null) {
  //           escapeValue = findModifyValue;
  //         } else {
  //           if (pageIndex < this.externalData.externalDataList.first.data.columns[jsonElement.bindingColumn].length) {
  //             escapeValue = this.externalData.externalDataList.first.data.columns[jsonElement.bindingColumn][pageIndex];
  //           }
  //         }
  //
  //         /// 是否添加标题
  //         if (addContentTitle && jsonElement is TextElement) {
  //           TextElement textElement = jsonElement;
  //           if ((textElement.contentTitle ?? '').length > 0) {
  //             return '${textElement.contentTitle}：$escapeValue';
  //           }
  //         }
  //         if(jsonElement is BarCodeElement && jsonElement.codeType == BarcodeType.CODE_BAR){
  //           if((escapeValue?.length ?? 0)>57){
  //             escapeValue = escapeValue.substring(0,57);
  //           }
  //         }
  //         return escapeValue;
  //       } else if (isGoodsLabelTemplate() && (jsonElement.fieldName ?? '').isNotEmpty) {
  //         /// 商品导入场景
  //         return goodsData[pageIndex].toJson()[jsonElement.fieldName];
  //       }
  //     } catch (_) {
  //       return jsonElement.value;
  //     }
  //   } else if (jsonElement is SerialElement) {
  //     SerialElement serialElement = jsonElement;
  //     String content =
  //     ((int.tryParse(serialElement.startNumber ?? '1') ?? 0) + serialElement.incrementValue * pageIndex)
  //         .toString()
  //         .padLeft((serialElement.startNumber ?? '').length, '0');
  //     String value = '${serialElement.prefix ?? ''}$content${serialElement.suffix ?? ''}';
  //     return value;
  //   }
  //   return jsonElement.value;
  // }
  /// 批量预览缓存
  Map<int, NetalImageResult> _batchPreviewCache = {};

  /// 清理批量预览缓存
  clearBatchPreviewCache() {
    _batchPreviewCache.clear();
  }

  /// 是否已缓存图像
  bool hasCacheImage(int pageIndex) {
    return _batchPreviewCache.containsKey(pageIndex);
  }

  /// 批量模板生成预览图
  Future<NetalImageResult> generateSmartLayoutPreview(BuildContext context, int pageIndex, Color printColor,
      {double preRatio = 8}) async {
    /// 调用图像库生成预览
    String printJson = await generatePrintJsonAsync(pageIndex, false, true);
    NetalImageResult imageResult = await generateImage(printJson, preRatio, printColor);
    return imageResult;
  }

  /// 批量模板生成预览图
  Future<NetalImageResult?> generateBatchTemplatePreview(BuildContext context, int pageIndex, Color? printColor,
      {double preRatio = 8}) async {
    if (_batchPreviewCache.containsKey(pageIndex)) {
      /// 命中缓存
      return _batchPreviewCache[pageIndex];
    }

    /// 调用图像库生成预览
    String printJson = await generatePrintJsonAsync(pageIndex, false, true);
    NetalImageResult imageResult = await generateImage(printJson, preRatio, printColor);
    _batchPreviewCache[pageIndex] = imageResult;
    return _batchPreviewCache[pageIndex];
  }

  static Future<NetalImageResult> generateImage(String jsonString, double preRatio, Color? printColor) async {
    return IsolateUtil().invoke<NetalImageResult, List<dynamic>>(generateImageSync, [jsonString, preRatio, printColor]);
  }

  static NetalImageResult generateImageSync(List<dynamic> params) {
    String jsonString = params[0];
    double preRatio = params[1];
    Color? printColor = params[2];

    return NiimbotNetal.generateImageFromPrintJson(
      jsonString: jsonString,
      ratio: preRatio,
      printRatio: DisplayUtil.pxRatio,
      printerMargin: [0, 0, 0, 0],
      printerOffset: [0, 0],
      orientation: 0,
      color: printColor,
    );
  }

  /// 记录 excel 修改项
  // modifyExcelData(String elementId, String value) {
  //   /***
  //       "task" : {
  //       "modifyData" : {
  //       "2758CB1B-E321-49DC-80ED-89FB8C40C8C7" :
  //       {  /// element id
  //       "0" : "沉香化气片888" /// 0 为页码
  //       },
  //       "6D9087D0-8F75-4B22-99F0-4A7A701A3D9B" :
  //       {
  //       "1" : "广东省四会888"
  //       },
  //       "2D5E7FDD-8389-4DC8-891C-080C85312430" :
  //       {
  //       "4" : "888"
  //       }
  //       },
  //       "externalDataID" : "1"
  //       },
  //    */
  //   TemplateTask originTask = task?.clone();
  //
  //   if (task == null) task = TemplateTask();
  //
  //   task.externalDataID = externalData.id;
  //
  //   if (task.modifyData == null) task.modifyData = {};
  //
  //   if (task.modifyData[elementId] == null) task.modifyData[elementId] = {};
  //
  //   task.modifyData[elementId]['$currentPageIndex'] = value;
  //
  //   // /// 暂存变更记录
  //   // StackManager().stashTaskRecord(originTask, task.clone());
  // }

  /// 复制元素同步Excel内容修改记录
  cloneElementSyncExcelTask(String originElementId, String cloneElementId) {
    // if (task == null || task.modifyData == null || task.modifyData[originElementId] == null) {
    //   return;
    // }
    // Map<String, String> originModifyMap = task.modifyData[originElementId];
    // Map<String, String> cloneModifyMap = {};
    // originModifyMap.keys.forEach((key) {
    //   cloneModifyMap[key] = originModifyMap[key];
    // });
    // task.modifyData[cloneElementId] = cloneModifyMap;

    if ((modify?.isEmpty ?? true) || modify?[originElementId] == null) {
      return;
    }
    Map<String, DataBindModify>? originModifyMap = modify![originElementId];
    Map<String, DataBindModify> cloneModifyMap = {};
    originModifyMap?.keys.forEach((key) {
      DataBindModify cloneBindModify = DataBindModify.fromJson(jsonDecode(jsonEncode(originModifyMap[key]?.toJson())));
      cloneModifyMap[key] = cloneBindModify;
    });
    modify![cloneElementId] = cloneModifyMap;
  }

  /// 清除修改记录
  // clearModifyRecord() {
  //   /// 暂存变更记录
  //   StackManager().stashTaskRecord(task?.clone(), null);
  //   task = null;
  // }

  /// 更新修改记录的 id
  /// 单元格拆分时, 合并单元格的 id 修改为第一个原始单元格的 id
  /// 单元格合并时, 第一个原始单元格的 id 修改为合并后单元格的 id
  // updateModifyRecordId(String originId, String newId) {
  //   try {
  //     TemplateTask originTask = task?.clone();
  //
  //     if (task != null && task.modifyData != null && task.modifyData[originId] != null) {
  //       task.modifyData[newId] = task.modifyData[originId];
  //       task.modifyData.remove(originId);
  //     }
  //
  //     // /// 暂存变更记录
  //     // StackManager().stashTaskRecord(originTask, jsonDecode(jsonEncode(task)));
  //   } catch (_) {}
  // }

  /// 移除单个元素的修改记录项
  bool removeModifyRecord(List<JsonElement> jsonElements) {
    // try {
    //   if (task == null || task.modifyData == null) {
    //     return false;
    //   }
    //   bool changed = false;
    //   TemplateTask originTask = task.clone();
    //
    //   jsonElements.forEach((element) {
    //     if (task.modifyData[element.id] != null) {
    //       task.modifyData.remove(element.id);
    //       changed = true;
    //     }
    //   });
    //
    //   return changed;
    //
    //   // /// 暂存变更记录
    //   // StackManager().stashTaskRecord(originTask, task?.clone());
    // } catch (_) {
    //   return false;
    // }

    try {
      if (modify?.isEmpty ?? true) {
        return false;
      }
      bool changed = false;
      jsonElements.forEach((element) {
        if (modify![element.id] != null) {
          modify!.remove(element.id);
          changed = true;
        }
      });
      return changed;
      // /// 暂存变更记录
      // StackManager().stashTaskRecord(originTask, task?.clone());
    } catch (_) {
      return false;
    }
  }

  parseBackground() async {
    try {
      String selectedLocalPath = localBackground.length > multipleBackIndex && multipleBackIndex >= 0
          ? localBackground[multipleBackIndex]
          : "";
      if (selectedLocalPath.isNotEmpty) {
        File localImage = File(selectedLocalPath);
        if (localImage.existsSync()) {
          // Uint8List result = await CanvasImageUtils.rotateImageFile(selectedLocalPath, canvasRotate);

          Uint8List result = localImage.readAsBytesSync();
          _logger.log('----------result: ${result}');
          backgroundImage = base64.encode(result);
        }
      }
    } catch (e, s) {
      _logger.log('异常信息:\n $e');
      _logger.log('调用栈信息:\n $s');
    }
  }

  String getBackImageUrl() {
    return localBackground.length > multipleBackIndex && multipleBackIndex >= 0
        ? localBackground[multipleBackIndex]
        : "";
  }

  Map<String, Uint8List> backgroundDataMap = {};

  Uint8List? getBackImageFile() {
    String selectedLocalPath = getBackImageUrl();
    File localImage = File(selectedLocalPath);
    if (backgroundDataMap[selectedLocalPath] == null && localImage.existsSync()) {
      backgroundDataMap.clear();
      backgroundDataMap[selectedLocalPath] = localImage.readAsBytesSync();
    }
    return backgroundDataMap[selectedLocalPath];
  }

  static Future<String> loadImageData(String data) async {
    List<ImageElement> imageElements = [];
    TemplateData templateData = TemplateData.fromJson(json.decode(data));
    final templateElements = templateData.elements;
    if (templateElements.isNotEmpty) {
      templateElements.forEach((element) {
        if (element is ImageElement) {
          imageElements.add(element);
        }
      });
    }
    compatibleSerialMirrorFromAndroid(templateData);

    if (imageElements.isNotEmpty) {
      // for (int i = 0; i < imageElements.length; i++) {
      //   ImageElement imageElement = imageElements[i];
      //   File file = File(imageElement.localUrl);
      //   if (file.existsSync()) {
      //     Uint8List data = await file.readAsBytes();
      //     imageElement.imageData = base64.encode(data);
      //   }
      // }
      // return json.encode(templateData.toJson());
    }
    return json.encode(templateData.toJson());
  }

  /// 兼容android的流水号镜像bug
  /// android流水号镜像的fixLength和主元素不一致
  static void compatibleSerialMirrorFromAndroid(TemplateData templateData) {
    Map<String, SerialElement> serialElements = {};
    List<SerialElement> serialMirrorElements = [];
    final templateElements = templateData.elements;
    if (templateElements.isNotEmpty) {
      templateElements.forEach((element) {
        if (element is SerialElement && (element.mirrorId ?? "").length > 0) {
          if (element.isOpenMirror == 1) {
            serialElements[element.mirrorId] = element;
          } else {
            serialMirrorElements.add(element);
          }
        }
      });
    }
    serialMirrorElements.forEach((mirror) {
      String mirrorId = mirror.id;
      mirror.fixLength = serialElements[mirrorId]!.fixLength;
      mirror.value = serialElements[mirrorId]!.value;
    });
  }

  static String parseDateElement(String data) {
    List<DateElement> dateElements = [];
    TemplateData templateData = TemplateData.fromJson(json.decode(data));
    final templateElements = templateData.elements;
    if (templateElements.isNotEmpty) {
      templateElements.forEach((element) {
        if (element is DateElement) {
          dateElements.add(element);
        }
      });
    }
    if (dateElements.isNotEmpty) {
      dateElements.forEach((element) {
        try {
          element.time = int.parse(element.value!);
        } catch (e) {
          element.time = DateTime.now().millisecondsSinceEpoch;
        }
      });
      return json.encode(templateData.toJson());
    }
    return data;
  }

  swapElementOrderByFocus(CanvasElement focusElement) {
    debugPrint("焦点定位：焦点元素ID: ${focusElement.elementId}");
    int? index = canvasElements.indexOf(focusElement);
    if (index >= 0) {
      canvasElements.remove(focusElement);
      canvasElements.add(focusElement);
    }
    canvasElements.forEach((element) {
      debugPrint("焦点定位：排序元素ID: ${element.elementId}");
    });
  }

  /// 替换模板
  TemplateData mergeTemplate(TemplateData target, {bool fromLabelSetting = false}) {
    //获取原始模版的长度
    int orginTemplateLen = 0;
    if (canvasRotate == 0 || canvasRotate == 180) {
      orginTemplateLen = width?.toInt() ?? 0;
    } else {
      orginTemplateLen = height?.toInt() ?? 0;
    }
    // target是标签纸所以canvasRotate为0，防止从原生映射时导致的脏数据
    target.canvasRotate = 0;
    target.elements = this.elements;
    target.canvasElements = this.canvasElements;
    target.profile.extrain.createTime = this.profile.extrain.createTime;
    target.profile.extrain.userId = this.profile.extrain.userId;
    Map<String, dynamic> mergeUseFont = this.usedFonts ?? {};
    target.usedFonts?.forEach((key, value) {
      if (mergeUseFont[key] == null) {
        mergeUseFont[key] = value;
      }
    });
    target.usedFonts = mergeUseFont;

    /// excel相关字段
    // target.externalData = this.externalData;
    // target.task = this.task;
    target.dataSource = this.dataSource;
    target.modify = this.modify;
    target.bindInfo = this.bindInfo;
    target.currentPageIndex = this.currentPageIndex;
    target.totalPage = this.totalPage;
    target.local_type = this.local_type;

    ///TODO: 2023/5/10 Ice_Liu  1.需要处理商品模板替换时商品元素变更
    ///TODO: 2023/5/10 Ice_Liu  2.需要处理source模板的画板旋转相关
    ///TODO: 2023/5/10 Ice_Liu  3.需要模板类型，labelId等参数
    if (this.profile.extrain.templateType == 1 ||
        (this.profile.extrain.templateType == 0 && this.profile.extrain.userId != "${CanvasUserCenter().userId}")) {
      target.originTemplateId = this.id.length > 10 ? "" : this.id;
      if (Platform.isIOS) {
        target.id = this.id;
        target.profile.extrain.templateType = this.profile.extrain.templateType;
      } else {
        target.id = "";
        target.profile.extrain.templateType = -2;
      }
    } else {
      target.id = this.id;
      target.profile.extrain.templateType = this.profile.extrain.templateType;
    }
    target.name = this.name;
    target.cloudTemplateId = this.cloudTemplateId;
    target.profile.extrain.folderId = this.profile.extrain.folderId;
    // 模版的宽高比(翻转后的)vs标签纸的宽高比
    if (height != null && target.height != null && width != null && target.width != null) {
      double templateAspectRatio = width! / height!;
      double labelAspectRatio = target.width! / target.height!;
      if ((templateAspectRatio > 1 && labelAspectRatio < 1) || (templateAspectRatio < 1 && labelAspectRatio > 1)) {
        target.canvasRotate += 90;
      }
    }

    if (target.canvasRotate != 0) {
      num? width = target.width;
      target.width = target.height;
      target.height = width;
      num rotate = target.rotate + target.canvasRotate / 90 * 270;
      target.rotate = (rotate % 360).toInt();
      // 线缆尾巴方向调整，一旦旋转，按照目前逻辑是右边翻转90度，所以+1
      if (target.cableDirection != null && target.cableDirection != -1) {
        target.cableDirection = (target.cableDirection! + 1) % 4;
      }
    }

    //如果目标是连续纸的情况下，按原始模版设置的长度进行恢复
    if (target.paperType == 3 && !fromLabelSetting) {
      if (target.canvasRotate == 0 || target.canvasRotate == 180) {
        target.width = orginTemplateLen;
      } else {
        target.height = orginTemplateLen;
      }
    }
    // target.layoutSchema = this.layoutSchema;
    // target.supportedEditors = this.supportedEditors;
    _logger.log("===================替换模板");
    return target;
  }

  canvasRotateReset() {
    if (this.canvasRotate % 180 != 0) {
      num? width = this.width;
      this.width = this.height;
      this.height = width;
    } else {
      return;
    }
    int times = this.canvasRotate ~/ 90;
    this.rotate = (this.rotate + times * 90) % 360;
    int? cableDirection = this.cableDirection?.toInt();
    if (cableDirection != null && cableDirection != -1) {
      cableDirection = (cableDirection - times % 4) % 4;
    }
    this.cableDirection = cableDirection;
    this.canvasRotate = 0;
  }

  canvasRotateBy({int targetRotate = 90}) {
    if (targetRotate != 0) {
      this.canvasRotate += targetRotate;
      num? width = this.width;
      this.width = this.height;
      this.height = width;
      num rotate = this.rotate + 270;
      this.rotate = (rotate % 360).toInt();
      // 线缆尾巴方向调整，一旦旋转，按照目前逻辑是右边翻转90度，所以+1
      if (this.cableDirection != null && this.cableDirection != -1) {
        this.cableDirection = (this.cableDirection! + 1 == 4) ? 0 : this.cableDirection! + 1;
        this.cableDirection = this.cableDirection! % 4;
      }
      this.canvasRotate = canvasRotate % 360;
    }
  }

  ///行业模板替换融合 只保留行业模板中元素数据及数据源相关数据
  mergeIndustryTemplate(TemplateData industryData) {
    elementIds = industryData.elementIds;
    elements = industryData.elements;
    canvasElements = industryData.canvasElements;
    externalData = industryData.externalData;
    task = industryData.task;
    dataSource = industryData.dataSource;
    dataBindingMode = industryData.dataBindingMode;
    bindInfo = industryData.bindInfo;
    modify = industryData.modify;
    currentPageIndex = industryData.currentPageIndex;
    totalPage = industryData.totalPage;
    templateVersion = industryData.templateVersion;
    return this;
  }

  /// 判断是否同一模板
  bool isMatchRFID(String barcode) {
    bool result = this.profile.barcode == barcode ||
        this.profile.extrain.sparedCode == barcode ||
        this.profile.extrain.virtualBarCode == barcode ||
        this.profile.extrain.amazonCodeBeijing == barcode ||
        this.profile.extrain.amazonCodeWuhan == barcode ||
        (this.profile.extrain.barcodeCategoryMap?.values.contains(barcode) ?? false);
    _logger.log("======RFID判断，matchResult：$result");
    return result;
  }

  String suffixFileName = ".ttf";

  updateUsedFont(TextElement textElement) async {
    // FontFileManager().findFileEx('${textElement.fontCode}$suffixFileName').then((exist) {
    //   if (!exist) {
    //     usedFonts.remove(textElement.fontCode);
    //     textElement.fontCode = "ZT001";
    //     textElement.fontFamily = "ZT001";
    //   }
    // });
  }

  Future<List<String>> checkNotDownloadFontCodes() async {
    List<String> fontCodes = <String>[];
    try {
      List<String> usedFontCodes = getUsedFontCodes();

      Directory fontDirectory = Directory(FontFileManager().fontPath);
      List<FileSystemEntity> fontEntities = fontDirectory.listSync();
      // List<String> fontPaths = [];
      // for (var fileEntity in fontEntities) {
      //   fontPaths.add(fileEntity.path);
      // }
      for (var code in usedFontCodes) {
        bool isExists = false;
        for (var fileEntity in fontEntities) {
          if (fileEntity.path.contains(code + ".")) {
            isExists = true;
            this.usedFonts?[code] = basename(fileEntity.path);
            break;
          }
        }
        if (!isExists) {
          fontCodes.add(code);
        }
      }
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
      debugPrint('调用栈信息:\n $s');
    }
    // for (var entry in usedFonts.entries) {
    //   _logger.log('checkNotDownloadFontCodes ${entry.key} : ${entry.value}');
    //   String fontCode = entry.key;
    //   if(!usedFontCodes.contains(fontCode)){
    //     continue;
    //   }
    //   String fontName = entry.value;
    //   if (fontCode != "fontDefault" && !FontManager().isDefaultFont(fontCode)) {
    //     bool result = await FontFileManager().findFileEx(fontName);
    //     if (!result) {
    //       if (!fontCodes.contains(fontCode)) {
    //         fontCodes.add(fontCode);
    //       }
    //     }
    //   }
    // }
    return fontCodes;
  }

  ///获取模板中使用的字体
  List<String> getUsedFontCodes() {
    List<String> result = [];
    var textElements = canvasElements.where((element) => element.data is TextElement).toList();
    textElements.forEach((element) {
      TextElement temp = element.data as TextElement;
      if ((temp.fontCode?.isNotEmpty ?? false) && !result.contains(temp.fontCode)) {
        result.add(temp.fontCode!);
      }
    });

    var tableElements = (canvasElements ?? []).where((element) => element.data is TableElement).toList();
    for (var j = 0; j < tableElements.length; j++) {
      TableElement temp = tableElements[j].data as TableElement;
      for (var i = 0; i < temp.cells.length; i++) {
        TableCellElement cell = temp.cells[i];
        if ((cell.fontCode?.isNotEmpty ?? false) && !result.contains(cell.fontCode)) {
          result.add(cell.fontCode!);
        }
      }
      for (var i = 0; i < temp.combineCells.length; i++) {
        TableCellElement cell = temp.combineCells[i];
        if ((cell.fontCode?.isNotEmpty ?? false) && !result.contains(cell.fontCode)) {
          result.add(cell.fontCode!);
        }
      }
    }
    return result;
  }

  ///返回未下载的canvas elements最好，因为下载后要刷新图像，就要清楚图像缓存
  List<CanvasElement> getUndownloadFontElement(List<String>? fontCodes) {
    List<CanvasElement> result = [];
    if (fontCodes == null || fontCodes.isEmpty) {
      return result;
    }
    var hasUndownloadFontElements = (canvasElements ?? []).where((element) => element.data is TextElement).toList();
    hasUndownloadFontElements.forEach((element) {
      TextElement temp = element.data as TextElement;
      if (fontCodes.contains(temp.fontCode)) {
        result.add(element);
      }
    });

    var tableElements = (canvasElements ?? []).where((element) => element.data is TableElement).toList();
    for (var j = 0; j < tableElements.length; j++) {
      TableElement temp = tableElements[j].data as TableElement;
      bool isAdd = false;
      for (var i = 0; i < temp.cells.length; i++) {
        TableCellElement cell = temp.cells[i];
        if (fontCodes.contains(cell.fontCode)) {
          result.add(tableElements[j]);
          isAdd = true;
          break;
        }
      }
      if (isAdd) {
        continue;
      }
      for (var i = 0; i < temp.combineCells.length; i++) {
        TableCellElement cell = temp.combineCells[i];
        if (fontCodes.contains(cell.fontCode)) {
          result.add(tableElements[j]);
          break;
        }
      }
    }
    return result;
  }

  ///模板中是否包含某个关键词
  bool isContainKeyWords(String keyWords) {
    bool isContain = false;
    if (name.isCaseInsensitiveContains(keyWords)) {
      isContain = true; //包含名称
    }
    profile.extrain.barcodeCategoryMap?.values.forEach((element) {
      if (element == keyWords) isContain = true; //包含条码
    });
    if (isContain) return isContain;
    for (var element in elements) {
      if (element is DateElement || element is SerialElement || element is ImageElement) continue;
      if ((element is QrCodeElement && ((element.isLive) || (element.isForm)))) continue; //类型不可是高级二维码
      if (element is TableElement) {
        List<String> combineIds = [];
        for (var combinecellElement in element.combineCells) {
          if ((combinecellElement.value ?? "").isCaseInsensitiveContains(keyWords) &&
              (combinecellElement.dataBind == null || (combinecellElement.dataBind ?? []).isEmpty)) {
            isContain = true; //表格被合并的单元格包含
          }
          combineIds.add(combinecellElement.id);
        }
        if (isContain) return isContain;
        for (var cellElement in element.cells) {
          if (combineIds.contains(cellElement.combineId)) continue;
          if ((cellElement.value ?? "").isCaseInsensitiveContains(keyWords) &&
              (cellElement.dataBind == null || (cellElement.dataBind ?? []).isEmpty)) {
            isContain = true; //表格单元格包含
            break;
          }
        }
        if (isContain) return isContain;
      }
      if ((element.value ?? "").isCaseInsensitiveContains(keyWords) &&
          (element.dataBind == null || (element.dataBind ?? []).isEmpty)) {
        isContain = true;
        break;
      }
    }
    return isContain;
  }

  void resetCanvasElement() {
    /// 转化为画板元素对象
    Iterable<CanvasElement> elementsList = [];
    elementsList = elements.map((e) => e.toCanvasElement());
    (canvasElements ?? [])
      ..clear()
      ..addAll(elementsList);
  }

  void resetElementFont(List<String>? unSaveFontCodes) {
    List<CanvasElement> unDownloadFontCanvasElements = getUndownloadFontElement(unSaveFontCodes);
    unDownloadFontCanvasElements.forEach((element) {
      if (element.elementType == ElementItemType.text ||
          element.elementType == ElementItemType.date ||
          element.elementType == ElementItemType.serial) {
        TextElement textElement = element.data as TextElement;
        usedFonts?.remove(textElement.fontCode);
        textElement.fontCode = "ZT001";
        textElement.fontFamily = "ZT001";
      } else if (element.elementType == ElementItemType.table) {
        final tableElement = element.data as TableElement;

        tableElement.cells.forEach((cell) {
          if (unSaveFontCodes?.contains(cell.fontCode) ?? false) {
            usedFonts?.remove(cell.fontCode);
            cell.fontCode = "ZT001";
            cell.fontFamily = "ZT001";
          }
        });
        tableElement.combineCells.forEach((cell) {
          if (unSaveFontCodes?.contains(cell.fontCode) ?? false) {
            usedFonts?.remove(cell.fontCode);
            cell.fontCode = "ZT001";
            cell.fontFamily = "ZT001";
          }
        });
      }
    });
  }

  ExternalData? cloneExternalData() {
    return externalData?.clone();
  }

  _completionFontDefault() {
    if (usedFonts != null && !usedFonts!.containsKey(FontManager.FONT_DEFAULT_KEY)) {
      String? languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
      if (languageType == "ar") {
        usedFonts![FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_ARIAL_LOCAL_FILE_NAME;
      } else if (languageType == "ja") {
        usedFonts![FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_JAPANESE_LOCAL_FILE_NAME;
      } else {
        usedFonts![FontManager.FONT_DEFAULT_KEY] = FontManager.DEFAULT_FONT_LOCAL_FILE_NAME;
      }
    }
  }

  DataSource? cloneDataSource() {
    if (dataSource?.isEmpty ?? true) {
      return null;
    }
    return TemplateUtils.cloneDataSource(dataSource!.first);
  }

  TemplateModify? cloneTemplateModify() {
    if (modify == null) {
      return null;
    }
    return TemplateUtils.cloneModify(modify);
  }

  String getExcelMd5() {
    if (dataSource?.isEmpty ?? true) {
      return "";
    }
    return dataSource!.first.hash;
  }

  static List<String> goodsInfnFieldDescName() {
    return GoodFieldManager().goodsInfoFieldDescName();
  }

  static List<String> goodsInfnFieldName() {
    return GoodFieldManager().goodsInfoFieldName();
  }

  factory TemplateData.fromDBJson(Map<String, dynamic> json) {
    if (Platform.isIOS) {
      List<String> paperColor;
      if (json['paperColor'] != null && json['paperColor'].isNotEmpty) {
        if (json['paperColor'] is List) {
          // paperColor = List<String>.from(json['paperColor']);
          paperColor = ["0.0.0", "230.0.18"];
        } else {
          paperColor = List<String>.from(jsonDecode(json['paperColor']));
        }
      } else {
        paperColor = ["0.0.0", "230.0.18"];
      }

      List<JsonElement>? elements = [];
      for (var item in (jsonDecode(json['elements']) as List? ?? [])) {
        if (item != null) {
          JsonElement e = JsonElement.build(Map<String, dynamic>.from(item));
          elements.add(e);
        }
      }

      List<String> elementIds = [];
      elements.forEach((element) {
        if (element.isOpenMirror == 1) {
          String newMirrorId = element.generateFixedMirrorId();
          final mirrorElement = elements.singleWhereOrNull((mirrorElement) {
            return mirrorElement.id == element.mirrorId && mirrorElement.id.isNotEmpty;
          });
          if (mirrorElement != null) {
            element.mirrorId = newMirrorId;
            mirrorElement.id = newMirrorId;
          }
        }
        if (elementIds.contains(element.id)) {
          element.id = JsonElement.generateId();
        } else {
          elementIds.add(element.id);
        }
      });
      var currentPageIndex;
      if (json['currentPage'] is num && json['currentPage'] > 0) {
        currentPageIndex = json['currentPage'] - 1;
      } else {
        currentPageIndex = json['currentPageIndex'] ?? 0;
      }

      String externalDataStr = json['externalData'];
      var modify;
      if (json['modify'] == null || json['modify'].isEmpty) {
        modify = null;
      } else {
        if (json['modify'] is Map) {
          modify = json['modify'];
        } else {
          Map<String, Map<String, DataBindModify>> result =
              TemplateModifyExtension.fromJson(Map<String, dynamic>.from(jsonDecode(json['modify'])));
          modify = result;
        }
      }
      var dataSource =
          (json['dataSource'] == null || json['dataSource'] == "" || (jsonDecode(json['dataSource']) as List).isEmpty)
              ? null
              : [DataSource.fromJson(Map<String, dynamic>.from((jsonDecode(json['dataSource']) as List).first))];

      var bindInfo;
      var totalPage = json['totalPage'] ?? 1;
      if (json['bindInfo'] == null || json['bindInfo'].isEmpty) {
        bindInfo = null;
      } else {
        if (json['bindInfo'] is Map) {
          modify = json['bindInfo'];
        } else {
          bindInfo = ExcelPageInfo.fromJson(Map<String, dynamic>.from(jsonDecode(json['bindInfo'])));
        }
      }
      if (dataSource != null && bindInfo.page == null) {
        bindInfo = ExcelPageInfo(page: currentPageIndex, total: totalPage);
      }
      if (bindInfo != null &&
          bindInfo.page != null &&
          currentPageIndex != bindInfo.page &&
          (json['currentPage'] is num && json['currentPage'] > 0)) {
        currentPageIndex = bindInfo.page - 1;
      }
      List<GoodsModel> goodsData = [];
      if (json['goodsData'] != null) {
        for (var item in (jsonDecode(json['goodsData']) as List? ?? [])) {
          if (item != null) {
            goodsData.add(GoodsModel.fromJson(Map<String, dynamic>.from(item)));
          }
        }
        Iterable<CanvasElement> elementsList = [];
        if (elements != null) {
          elementsList = elements.map((e) => e.toCanvasElement());
        }
      }
      Iterable<CanvasElement> elementsList = [];
      elementsList = elements.map((e) => e.toCanvasElement());
      var externalData = json['externalData'] == null || json['externalData'] == 'null'
          ? null
          : ExternalData.fromJson(Map<String, dynamic>.from(jsonDecode(json['externalData'])));
      var dataBindingMode;
      final fieldCanvasElements = elementsList.where((element) => (element.data.fieldName ?? '').length > 0).toList();

      /// 包含的商品字段数量
      final goodsLabelsCount = (fieldCanvasElements ?? []).length;
      if (goodsLabelsCount > 0) {
        dataBindingMode = DataBindingMode.commodity;
      } else if ((externalData?.externalDataList?.isNotEmpty ?? false) &&
          (externalData?.externalDataList?.first.data?.columns.isNotEmpty ?? false) &&
          ((externalData?.externalDataList?.first.data?.columns.first.length ?? 0) > 0)) {
        dataBindingMode = DataBindingMode.excel;
      }

      if (dataSource != null && dataSource.isNotEmpty) {
        dataBindingMode = dataSource.first.type.getStringValue();
      } else {
        dataBindingMode = DataBindingMode.none;
      }

      // _completionFontDefault();

      return TemplateData(
        id: json['idStr'] == null ? "" : json['idStr'].toString(),
        name: json['name'] ?? "",
        layoutSchema: json['layoutSchema'] ?? "",
        names: (jsonDecode(json['names']) as List<dynamic>)
                .map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        labelNames: (jsonDecode(json['labelNames']) as List<dynamic>)
                .map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        thumbnail: json['thumbnail'] ?? "",
        backgroundImage: json['backgroundImage'] ?? "",
        multipleBackIndex: json['multipleBackIndex'] ?? 0,
        height: json['height'],
        width: json['width'],
        rotate: json['rotate'],
        consumableType: json['consumableType'] ?? -1,
        paperType: json['paperType'] ?? 0,
        isCable: (json['isCable'] is bool) ? json['isCable'] : json['isCable'] == 1,
        cableDirection: json['cableDirection'],
        cableLength: json['cableLength'] ?? 0.0,
        margin: json['margin'] == null
            ? [0, 0, 0, 0]
            : (jsonDecode(json['margin']) as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
        profile: Profile.fromDBJson(Map<String, dynamic>.from(json)),
        labelId: json['labelId'] == null ? null : json['labelId'] as String,
        local_type: json['localType'] == null ? 0 : json['localType'] as num,
        platformCode: json['platformCode'] == null ? "" : json['platformCode'],
        paccuracyName: json['paccuracyName'],
        commodityTemplate:
            (json['commodityTemplate'] is bool) ? json['commodityTemplate'] : json['commodityTemplate'] == 1,
        canvasRotate: json['canvasRotate'] ?? 0,
        originTemplateId: json['originTemplateId'] == null ? "" : json['originTemplateId'] as String,
        contentThumbnail: json['contentThumbnail'] == null ? "" : json['contentThumbnail'] as String,
        localContentThumb: json['localContentThumb'] == null ? "" : json['localContentThumb'] as String,
        version: json['version'],
        paperColor: paperColor,
        elements: elements,
        business: json['business'],
        currentPageIndex: currentPageIndex,
        totalPage: totalPage,
        localBackground: (json['localBackground'] == null)
            ? []
            : (jsonDecode(json['localBackground']) as List<dynamic>).map((e) => e as String).toList(),
        usedFonts: (json['usedFonts'] == null)
            ? {"ZT001": "ZT001.ttf"}
            : (Map<String, dynamic>.from(jsonDecode(json['usedFonts']))).isEmpty
                ? {"ZT001": "ZT001.ttf"}
                : Map<String, dynamic>.from(jsonDecode(json['usedFonts'])),
        externalData: externalData,
        task: json['task'] == null || json['task'] == 'null'
            ? null
            : TemplateTask.fromJson(Map<String, dynamic>.from(jsonDecode(json['task']))),
        dataSource: dataSource,
        modify: modify,
        templateVersion: json['templateVersion'],
        consumableTypeTextId: json['consumableTypeTextId'],
        goodsData: goodsData,
        canvasElements: elementsList.toList(),
        // hasVIPRes: (json['hasVIPRes'] == null)
        //     ? false
        //     : (json['hasVIPRes'] is bool)
        //         ? json['hasVIPRes']
        //         : json['hasVIPRes'] == 1,
        hasVIPRes: getHasVIPResValue(json),
        vip: (json['vip'] == null)
            ? false
            : (json['vip'] is bool)
                ? json['vip']
                : json['vip'] == 1,
        isEdited: json['isEdited'] is String ? (num.tryParse(json['isEdited']) ?? 0) : json['isEdited'] ?? 0,
        dataBindingMode: dataBindingMode,
      );
    } else {
      var printMoudle = PrintModule.fromJson(jsonDecode(json['JSON']));
      List<JsonElement>? elements = printMoudle.actionModel.models;

      // backgroundImage = printMoudle.background;
      // multipleBackIndex = printMoudle.multipleBackIndex;
      var paperColor;
      if (json['PAPER_COLOR'] != null && json['PAPER_COLOR'].isNotEmpty) {
        if (json['PAPER_COLOR'] is List) {
          paperColor = ["0.0.0", "230.0.18"];
        } else {
          paperColor = json['PAPER_COLOR'].toString().split(",");
        }
      } else {
        paperColor = ["0.0.0", "230.0.18"];
      }

      var currentPageIndex;
      if (json['PAGE_NUMBER'] is num && json['PAGE_NUMBER'] > 0) {
        currentPageIndex = json['PAGE_NUMBER'] - 1;
      } else {
        currentPageIndex = 0;
      }
      Iterable<CanvasElement> elementsList = [];
      elementsList = elements.map((e) => e.toCanvasElement());

      var totalPage = json['PAGE_TOTAL'] ?? 1;
      var dataSource = (json['DATA_SOURCES_JSON'] == null ||
              json['DATA_SOURCES_JSON'] == 'null' ||
              json['DATA_SOURCES_JSON'] == "" ||
              (jsonDecode(json['DATA_SOURCES_JSON']) as List).isEmpty)
          ? null
          : [DataSource.fromJson(Map<String, dynamic>.from((jsonDecode(json['DATA_SOURCES_JSON']) as List).first))];
      var modify;
      if (json['DATA_SOURCE_MODIFIES_JSON'] == null ||
          json['DATA_SOURCE_MODIFIES_JSON'].isEmpty ||
          json['DATA_SOURCE_MODIFIES_JSON'] == "null") {
        modify = null;
      } else {
        if (json['DATA_SOURCE_MODIFIES_JSON'] is Map) {
          modify = json['DATA_SOURCE_MODIFIES_JSON'];
        } else {
          Map<String, Map<String, DataBindModify>> result = TemplateModifyExtension.fromJson(
              Map<String, dynamic>.from(jsonDecode(json['DATA_SOURCE_MODIFIES_JSON'])));
          modify = result;
        }
      }
      var bindInfo;
      if (json['DATA_SOURCE_BIND_INFO_JSON'] == null ||
          json['DATA_SOURCE_BIND_INFO_JSON'].isEmpty ||
          json['DATA_SOURCE_BIND_INFO_JSON'] == "null") {
        bindInfo = null;
      } else {
        if (json['DATA_SOURCE_BIND_INFO_JSON'] is Map) {
          modify = json['DATA_SOURCE_BIND_INFO_JSON'];
        } else {
          bindInfo = ExcelPageInfo.fromJson(Map<String, dynamic>.from(jsonDecode(json['DATA_SOURCE_BIND_INFO_JSON'])));
        }
      }
      if (dataSource != null && bindInfo.page == null) {
        bindInfo = ExcelPageInfo(page: currentPageIndex, total: totalPage);
      }
      if (bindInfo != null &&
          bindInfo.page != null &&
          currentPageIndex != bindInfo.page &&
          (json['PAGE_NUMBER'] is num && json['PAGE_NUMBER'] > 0)) {
        currentPageIndex = bindInfo.page - 1;
      }

      // goodsData = (json['goodsData'] == null || json['goodsData'].isEmpty)
      //     ? null
      //     : (jsonDecode(json['goodsData']) as List)
      //         ?.map((e) => e == null ? null : GoodsModel.fromJson(Map<String, dynamic>.from(e)))
      //         ?.toList();
      // Iterable<CanvasElement> elementsList = [];
      // if (elements != null) {
      //   elementsList = elements.map((e) => e.toCanvasElement());
      // }
      // canvasElements
      //   ..clear()
      //   ..addAll(elementsList);
      // if (goodsLabelsCount() > 0) {
      //   dataBindingMode = DataBindingMode.commodity;
      // } else if ((externalData?.externalDataList?.isNotEmpty ?? false) &&
      //     ((externalData?.externalDataList?.first?.data?.columns?.first?.length ?? 0) > 0)) {
      //   dataBindingMode = DataBindingMode.excel;
      // }

      var dataBindingMode;
      if (dataSource != null && dataSource.isNotEmpty) {
        dataBindingMode = dataSource.first.type.getStringValue();
      } else {
        dataBindingMode = DataBindingMode.none;
      }

      // _completionFontDefault();
      return TemplateData(
        name: json['NAME'] ?? "",
        cloudTemplateId: json['CLOUD_TEMPLATE_ID'] ?? "",
        id: json['ID'] == null ? "" : json['ID'].toString(),
        names: (jsonDecode(json['NAMES']) as List<dynamic>)
                .map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        layoutSchema: json['LAYOUT_SCHEMA'] ?? "",
        labelNames: (jsonDecode(json['LABEL_NAMES']) as List<dynamic>)
                .map((e) => LabelNameInfo.fromJson(Map<String, dynamic>.from(e)))
                .toList() ??
            [],
        thumbnail: json['THUMB'] ?? "",
        localThumb: json['LOCAL_THUMB'] ?? "",
        height: json['HEIGHT'],
        width: json['WIDTH'],
        rotate: json['ROTATE'],
        elements: printMoudle.actionModel.models ?? [],
        backgroundImage: printMoudle.background,
        multipleBackIndex: printMoudle.multipleBackIndex,
        localBackground: printMoudle.localBackground ?? [],
        consumableType: json['SUPPLIES_TYPE'],
        paperType: json['TYPE'] ?? 0,
        isCable: (json['IS_CABLE'] is bool) ? json['IS_CABLE'] : json['IS_CABLE'] == 1,
        cableDirection: json['CABLE_DIRECTION'],
        cableLength: json['CABLE_LENGTH'] ?? 0.0,
        margin: json['MARGIN'] == null
            ? [0, 0, 0, 0]
            : (jsonDecode(json['MARGIN']) as List<dynamic>).map((e) => (e as num).toDouble()).toList(),
        profile: Profile.fromDBJson(Map<String, dynamic>.from(json)),
        labelId: json['LABEL_ID'] == null ? null : json['LABEL_ID'] as String,
        local_type: json['LOCAL_TYPE'] == null ? 0 : json['LOCAL_TYPE'] as num,
        platformCode: json['PLATFORM_CODE'] == null ? "" : json['PLATFORM_CODE'],
        paccuracyName: json['PACCURACY_NAME'],
        commodityTemplate:
            (json['COMMODITY_TEMPLATE'] is bool) ? json['COMMODITY_TEMPLATE'] : json['COMMODITY_TEMPLATE'] == 1,
        canvasRotate: json['CANVAS_ROTATE'] ?? 0,
        originTemplateId: json['ORIGIN_TEMPLATE_ID'] == null ? "" : json['ORIGIN_TEMPLATE_ID'] as String,
        contentThumbnail: json['CONTENT_THUMBNAIL'] == null ? "" : json['CONTENT_THUMBNAIL'] as String,
        localContentThumb: json['LOCAL_CONTENT_THUMB'] == null ? "" : json['LOCAL_CONTENT_THUMB'] as String,
        version: json['VERSION'],
        paperColor: paperColor,
        currentPageIndex: currentPageIndex,
        totalPage: totalPage,
        usedFonts: (json['usedFonts'] == null)
            ? {"ZT001": "ZT001.ttf"}
            : (Map<String, dynamic>.from(jsonDecode(json['usedFonts']))).isEmpty
                ? {"ZT001": "ZT001.ttf"}
                : Map<String, dynamic>.from(jsonDecode(json['usedFonts'])),
        externalData: json['EXTERNAL_DATA_JSON'] == null || json['EXTERNAL_DATA_JSON'] == 'null'
            ? null
            : ExternalData.fromJson(Map<String, dynamic>.from(jsonDecode(json['EXTERNAL_DATA_JSON']))),
        task: json['TASK_JSON'] == null || json['TASK_JSON'] == 'null'
            ? null
            : TemplateTask.fromJson(Map<String, dynamic>.from(jsonDecode(json['TASK_JSON']))),
        dataSource: dataSource,
        modify: modify,
        bindInfo: bindInfo,
        templateVersion: json['TEMPLATE_VERSION'] ?? "",
        consumableTypeTextId: json['CONSUMABLE_TYPE_TEXT_ID'],
        hasVIPRes: (json['HAS_VIP_RES'] == null || json['HAS_VIP_RES'] == "null")
            ? false
            : (json['HAS_VIP_RES'] is bool)
                ? json['HAS_VIP_RES']
                : json['HAS_VIP_RES'] == 1,
        vip: (json['VIP'] == null)
            ? false
            : (json['VIP'] is bool)
                ? json['VIP']
                : json['VIP'] == 1,
        isEdited: json['IS_EDITED'] is String ? (num.tryParse(json['IS_EDITED']) ?? 0) : json['IS_EDITED'] ?? 0,
        dataBindingMode: dataBindingMode,
        canvasElements: elementsList.toList(),
      )..localThumbnail = json["LOCAL_THUMB"];
    }
  }

  static bool getHasVIPResValue(Map<String, dynamic> json) {
    if (json['hasVIPRes'] != null) {
      if (json['hasVIPRes'] is bool) {
        return json['hasVIPRes'];
      } else {
        return json['hasVIPRes'] == 1;
      }
    } else if (json['hasVipRes'] != null) {
      if (json['hasVipRes'] is bool) {
        return json['hasVipRes'];
      } else {
        return json['hasVipRes'] == 1;
      }
    }
    return false;
  }

  Map<String, dynamic> toDBJson() {
    Map barcodeCategoryMap = profile.extrain.barcodeCategoryMap ?? {};
    if (Platform.isIOS) {
      barcodeCategoryMap.keys.forEach((element) {
        String codeValue =
            (barcodeCategoryMap[element] is int) ? barcodeCategoryMap[element].toString() : barcodeCategoryMap[element];
        barcodeCategoryMap[element] = '*$codeValue*';
      });
      return {
        'idStr': id ?? '',
        'cloudTemplateId': cloudTemplateId ?? '',
        'name': name ?? '',
        'names': jsonEncode(names ?? []),
        'labelNames': jsonEncode(labelNames ?? []),
        'thumbnail': thumbnail ?? '',
        'backgroundImage': backgroundImage ?? '',
        'multipleBackIndex': multipleBackIndex ?? 0,
        'width': width?.digits(2),
        'height': height?.digits(2),
        'rotate': rotate,
        'layoutSchema': layoutSchema,
        'consumableType': consumableType,
        'paperType': paperType ?? 0,
        'isCable': isCable ? 1 : 0,
        'cableLength': cableLength,
        'cableDirection': cableDirection,
        'margin': jsonEncode(margin ?? [0, 0, 0, 0]),
        'usedFonts': jsonEncode(usedFonts ?? {"ZT001": "ZT001.ttf"}),
        'elements': jsonEncode(getElements().map((e) => e.toJson()).toList()),
        "barcode": profile.barcode ?? "",
        "machineId": profile.machineId == null ? "" : "*${profile.machineId.replaceAll(",", "*")}*",
        "machineName": profile.machineName ?? "",
        "userId": profile.extrain.userId ?? "",
        "adaptPlatformCode": profile.extrain.adaptPlatformCode ?? "",
        "adaptPlatformName": profile.extrain.adaptPlatformName ?? "",
        "amazonCodeBeijing": profile.extrain.amazonCodeBeijing ?? "",
        "materialModelSn": profile.extrain.materialModelSn ?? "",
        "amazonCodeWuhan": profile.extrain.amazonCodeWuhan ?? "",
        "barcodeCategoryMap": jsonEncode(barcodeCategoryMap),
        "createTime": profile.extrain.createTime ?? "",
        "folderId": profile.extrain.folderId ?? "",
        "goodsCode": profile.extrain.goodsCode ?? "",
        "industryId": profile.extrain.industryId ?? "",
        "isCustom": profile.extrain.isCustom ?? false ? 1 : 0,
        "isDelete": profile.extrain.isDelete ?? false ? 1 : 0,
        "isMobileTemplete": profile.extrain.isMobileTemplete ?? false ? 1 : 0,
        "isPrivate": profile.extrain.isPrivate ?? false ? 1 : 0,
        "labelId": profile.extrain.labelId ?? "",
        "resourceVersion": profile.extrain.resourceVersion ?? "",
        "sourceId": profile.extrain.sourceId ?? "",
        "sparedCode": profile.extrain.sparedCode ?? "",
        "templateClass": profile.extrain.templateClass ?? "",
        "templateType": profile.extrain.templateType ?? "",
        "updateTime": profile.extrain.updateTime ?? "",
        "virtualBarCode": profile.extrain.virtualBarCode ?? "",
        'currentPage': getCurrentPage(),
        'totalPage': totalPage ?? 1,
        'platformCode': platformCode ?? "",
        'paccuracyName': paccuracyName ?? 203,
        'commodityTemplate': commodityTemplate ?? false ? 1 : 0,
        'contentThumbnail': contentThumbnail ?? '',
        'version': version ?? '',
        "localType": local_type,
        'originTemplateId': originTemplateId ?? '',
        'canvasRotate': canvasRotate,
        'externalData':
            externalData == null || externalData!.externalDataList == null ? "{}" : jsonEncode(externalData?.toJson()),
        'task': task == null ? "{}" : jsonEncode(task?.toJson()),
        'dataSource': (dataSource == null || dataSource!.isEmpty) ? "[]" : jsonEncode([dataSource?.first.toJson()]),
        'modify': modify == null ? "{}" : jsonEncode(modify?.toJson()),
        'bindInfo': bindInfo == null ? "{}" : jsonEncode(bindInfo?.toJson()),
        'templateVersion': templateVersion,
        'consumableTypeTextId': consumableTypeTextId,
        'paperColor': jsonEncode(paperColor),
        'vip': vip ? 1 : 0,
        'hasVIPRes': hasVIPRes ? 1 : 0,
        'isEdited': isEdited ?? "0",
        'isPrintHistory': 0,
      };
    } else {
      PrintModule printModule = PrintModule();
      printModule.background = this.backgroundImage ?? "";
      printModule.multipleBackIndex = this.multipleBackIndex;
      printModule.localBackground?.addAll(this.localBackground);
      ActionModelBean actionModelBean = ActionModelBean();
      List<JsonElement> baseItemModuleList = [];
      this.elements.forEach((element) {
        baseItemModuleList.add(element);
      });
      actionModelBean.models = baseItemModuleList;
      printModule.actionModel = actionModelBean;
      var json = jsonEncode(printModule.toJson());
      return {
        'ID': id ?? '',
        'FOLDER_ID': profile.extrain.folderId ?? '',
        'ADD_TIME': profile.extrain.createTime ?? '',
        'CABLE_DIRECTION': cableDirection ?? 0,
        'CABLE_LENGTH': cableLength ?? 0,
        'LOCAL_THUMB': localThumbnail ?? '',
        'LOCAL_CLOUD_ID': '',
        'IS_NEED_MOVE': false,
        'HARDWARE_SERIES_ID': profile.hardwareSeriesId != null ? jsonEncode(profile.hardwareSeriesId!.split(",")) : '',
        'LOCAL_TYPE': local_type,
        'IS_MOVE': 0,
        'LOCAL_PRIVATE': 0,
        'LAYOUT_SCHEMA': layoutSchema,
        'CAT_ID': 0,
        'CLICK_NUM': profile.extrain.clickNum ?? 0,
        'DOWNLOAD_COUNT': profile.extrain.downloadCount ?? 0,
        'EXCEL_ID': '',
        'HEIGHT': height ?? 0.0,
        'INDUSTRY_ID': profile.extrain.industryId ?? 0,
        'IS_DEL': profile.extrain.isDelete ?? false ? 1 : 0,
        'IS_HOT': profile.extrain.isHot ?? false ? 1 : 0,
        'IS_COM': 0,
        'IS_PRIVATE': profile.extrain.isPrivate ?? false ? 1 : 0,
        'JSON': json,
        'KEYWORDS': profile.keyword ?? "",
        'MACHINE_NAME': profile.machineName ?? '',
        "NAME": name ?? '',
        "ONE_CODE": profile.barcode ?? '',
        "ROTATE": rotate ?? 0,
        "SHOW_BACKGROUND": '',
        "SORT": profile.extrain.sortDependency!.isNotEmpty ? int.parse(profile.extrain.sortDependency!) : 0,
        "TEMPLATE_TYPE": profile.extrain.templateType,
        "TEMPLATE_CLASS": profile.extrain.templateClass,
        "THUMB": thumbnail ?? '',
        "TYPE": paperType ?? 0,
        "UPDATE_TIME": profile.extrain.updateTime,
        "USER_ID": profile.extrain.userId ?? 0,
        "WIDTH": width,
        "IS_SHOW_TAIL": consumableType == 1 && isCable,
        "AMAZON_CODE": profile.extrain.amazonCodeBeijing ?? '',
        "AMAZON_CODE_WUHAN": profile.extrain.amazonCodeWuhan ?? '',
        "SPARED_CODE": profile.extrain.sparedCode ?? '',
        "VIRTUAL_BAR_CODE": profile.extrain.virtualBarCode ?? '',
        "PAGE_TOTAL": totalPage ?? 1,
        "PAGE_NUMBER": getCurrentPage(),
        "SUPPLIES_TYPE": consumableType ?? 0,
        "MARGIN": jsonEncode(margin),
        "DESCRIPTION": description ?? '',
        "EXTERNAL_DATA_JSON": jsonEncode(externalData?.toJson()),
        "TASK_JSON": jsonEncode(task?.toJson()),
        'DATA_SOURCES_JSON':
            (dataSource == null || dataSource!.isEmpty) ? null : jsonEncode([dataSource?.first.toJson()]),
        'DATA_SOURCE_MODIFIES_JSON': jsonEncode(modify?.toJson()),
        'DATA_SOURCE_BIND_INFO_JSON': jsonEncode(bindInfo?.toJson()),
        'TEMPLATE_VERSION': templateVersion ?? '',
        'CONSUMABLE_TYPE_TEXT_ID': consumableTypeTextId,
        'MATERIAL_MODEL_SN': profile.extrain.materialModelSn ?? '',
        'IS_CABLE': isCable,
        'FROM_OLD_VERSION': 0,
        'PACCURACY_NAME': paccuracyName ?? 0,
        'PLATFORM_CODE': platformCode ?? '',
        'HAS_VIP_RES': hasVIPRes,
        'COMMODITY_TEMPLATE': commodityTemplate,
        'RESOURCE_VERSION': profile.extrain.resourceVersion ?? '',
        'VERSION': version ?? '',
        'VIP': vip,
        'CANVAS_ROTATE': canvasRotate ?? 0,
        'ORIGIN_TEMPLATE_ID': originTemplateId ?? '',
        //  'COMMODITY_MODULE': paperColor------,
        'SOURCE_ID': profile.extrain.sourceId ?? '',
        'LABEL_ID': profile.extrain.labelId ?? '',
        'ADAPT_PLATFORM_CODE': profile.extrain.adaptPlatformCode ?? '',
        'ADAPT_PLATFORM_NAME': profile.extrain.adaptPlatformName ?? '',
        "IS_NEW_PATH": profile.extrain.isNewPath,
        "IS_MOBILE_TEMPLETE": profile.extrain.isMobileTemplete,
        "PAPER_COLOR": paperColor.join(","),
        "CLOUD_TEMPLATE_ID": cloudTemplateId,
        "PRINT_METHOD_CODE": templatePrintMode ?? 0,
        "PRINTED_PROCESS_LIST": null,
        "BARCODE_CATEGORY_MAP": profile.extrain.barcodeCategoryMap,
        "NAMES": jsonEncode(names),
        "LABEL_NAMES": jsonEncode(labelNames),
        "IS_EDITED": isEdited ?? 0,
        "CONTENT_THUMBNAIL": contentThumbnail,
        "LOCAL_CONTENT_THUMB": localContentThumb,
        "MACHINE_ID": profile.machineId,
        "PHONE": profile.extrain.phone ?? '',
        "GOODS_CODE": profile.extrain.goodsCode ?? '',
      };
    }
  }

  String getPrintModule(TemplateData templateModuleLocal) {
    PrintModule printModule = PrintModule();
    printModule.background = templateModuleLocal.backgroundImage ?? "";
    printModule.multipleBackIndex = templateModuleLocal.multipleBackIndex;
    printModule.localBackground?.addAll(templateModuleLocal.localBackground);
    ActionModelBean actionModelBean = ActionModelBean();
    List<JsonElement> baseItemModuleList = [];
    templateModuleLocal.elements.forEach((element) {
      baseItemModuleList.add(element);
    });
    actionModelBean.models = baseItemModuleList;
    printModule.actionModel = actionModelBean;
    return jsonEncode(printModule.toJson());
  }

  //获取标签纸详细信息
  void getLableDetail() {}

// getBaseItemMoudle(JsonElement element) {
//   BaseElementModel model = BaseElementModel();
//   model.isForm = element;
//   return model;
// }
  static TemplateData generateSmartJson(String sourceJson, List<ElementCreateRequiredModel> elementCreateList) {
    TemplateData smartTemplate = TemplateData.fromJson(jsonDecode(sourceJson));
    smartTemplate.dataSource = null;
    smartTemplate.elements.clear();
    double offsetY = 5.0;
    elementCreateList.forEach((e) {
      Rect? location = e.location;
      if ((location ?? Rect.zero) == Rect.zero) {
        location = Rect.fromLTWH(5.0, offsetY, 30.0, 10.0);
      }
      if (e.bindElementType == ElementItemType.text) {
        smartTemplate.elements
          ..add(TextElement(
              id: JsonElement.generateId(),
              x: location!.left,
              y: location.top,
              width: location.width,
              height: location.height,
              value: e.value ?? '',
              fontSize: 3.2,
              rotate: 0,
              fontStyle: [],
              textAlignHorizontal: 2,
              letterSpacing: 0,
              lineSpacing: 0,
              wordSpacing: 0,
              lineMode: 2,
              lineBreakMode: 1,
              textAlignVertical: 1,
              zIndex: 0,
              boxStyle: TextElementBoxStyle.autoWidth));
        offsetY += (location.height - 5.0);
      }
    });
    smartTemplate.resetCanvasElement();
    return smartTemplate;
  }

  /// 模板替换自动排版时补齐镜像元素
  void completeMirrorElement() {
    // 画板中心点
    final templateCenter = Offset(width! / 2.0, height! / 2.0);

    // 创建新的元素列表
    final newElements = <JsonElement>[];
    final newCanvasElements = <CanvasElement>[];

    // 遍历元素列表并分类处理
    elements.removeWhere((jsonElement) {
      if (jsonElement.isOpenMirror == 1) {
        // 生成镜像元素
        final mirrorElement = generateMirrorElement(templateCenter, jsonElement);
        newElements.add(mirrorElement);
        newCanvasElements.add(mirrorElement.toCanvasElement());
      } else if (jsonElement.isMirrorElement()) {
        // 标记为需要移除的元素
        return true;
      }
      return false;
    });

    // 添加新生成的镜像元素
    elements.addAll(newElements);
    canvasElements.addAll(newCanvasElements);
  }

  JsonElement generateMirrorElement(Offset templateCenter, JsonElement jsonElement) {
    JsonElement mirrorElement = jsonElement.clone();
    mirrorElement.id = jsonElement.generateFixedMirrorId();
    jsonElement.mirrorId = mirrorElement.id;
    mirrorElement.mirrorId = jsonElement.id;
    mirrorElement.isOpenMirror = 0;
    if (jsonElement.isBindingExcel()) {
      // _templateData.cloneElementSyncExcelTask(jsonElement.id, mirrorElement.id);
    }
    if ((jsonElement.mirrorType ?? 0) > 0) {
      /// x、y 轴镜像方式下，内容不做旋转
      mirrorElement.rotate = mirrorElement.rotate;
    } else {
      mirrorElement.rotate = (jsonElement.rotate + 180) % 360;
    }

    Offset distanceToCenter = templateCenter -
        Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

    Offset mirrorPosition = templateCenter + distanceToCenter;
    if (jsonElement.mirrorType == 1) {
      /// 1: 画板中心 y 轴镜像
      mirrorElement.y = mirrorPosition.dy;
    } else if (jsonElement.mirrorType == 2) {
      /// 2: 画板中心 x 轴镜像
      mirrorElement.x = mirrorPosition.dx;
    } else {
      /// 0: 画板中心点镜像
      mirrorElement.x = mirrorPosition.dx;
      mirrorElement.y = mirrorPosition.dy;
    }
    return mirrorElement;
  }

  /// 重置模板本地路径 仅限于模板数据迁移时IOS使用,其他情况请勿调用-----------------------------
  /// Reset template local paths for background images and element images
  Future<void> resetTemplateLocalPathFlutterIOS() async {
    List<String> imagePaths = [];
    String imageId = id;
    bool isCloud = profile.extrain.templateType == 1 ? true : false;
    // Get documents directory path
    String documentsPath = await getApplicationDocumentsDirectory().then((dir) => dir.path);
    // Handle background images
    List<String> backgroundImages = backgroundImage.split(',');
    if (backgroundImages.length > 1) {
      // Multiple background images
      for (int i = 0; i < backgroundImages.length; i++) {
        String imagePath = _getBackgroundImagePath(documentsPath, imageId, isCloud, i);
        imagePaths.add(imagePath);
      }
    } else {
      // Single background image
      String imagePath = _getBackgroundImagePath(documentsPath, imageId, isCloud);
      imagePaths.add(imagePath);
    }

    // Handle rotated images if needed
    // if (canvasRotate != 0 && needLoadRotateImage) {
    //   imagePaths = _getRotatedImagePaths(imageId, isCloud);
    //   needLoadRotateImage = false;
    // }

    localBackground = imagePaths;
    localThumbnail = _getLocalThumbnailImagePath(documentsPath, id, isCloud);
    // Handle element images
    for (JsonElement element in elements) {
      if (element is ImageElement) {
        String id = element.isNinePatch == true
            ? element.id
            : ((element.materialId ?? "").isEmpty ? element.id : element.materialId!);
        debugPrint("迁移:元素图id: $id");
        if (element.localUrl.isEmpty || !File(element.localUrl).existsSync()) {
          debugPrint("迁移:元素图不存在path: ${element.localUrl} 重新获取");
          element.localUrl = _getElementImagePath(documentsPath, id, isCloud);
        }
        // Handle nine-patch images
        if (element.isNinePatch == true) {
          element.ninePatchLocalUrl = _getNinePatchImagePath(
              documentsPath, (element.materialId ?? "").isEmpty ? element.id : element.materialId!);
        }
      }
    }
  }

  String _getLocalThumbnailImagePath(String documentsPath, String imageId, bool isCloud) {
    String path = "";
    String folderName1 = "";
    String folderName2 = "";
    if (isCloud) {
      folderName1 = "Clould_Template";
      folderName2 = "Clould_ThumbBack";
    } else {
      folderName1 = "Template";
      folderName2 = "ThumbBack";
    }
    path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_zh-cn.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:缩略图存在path: $path");
      return path;
    }
    path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_en.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:缩略图存在path: $path");
      return path;
    }
    if (!isCloud) {
      path = _getLocalThumbnailImagePath(documentsPath, imageId, true);
      debugPrint("迁移:缩略图不存在path: $path 云模板文件夹中重新获取");
    }
    return "";
  }

  String _getBackgroundImagePath(String documentsPath, String imageId, bool isCloud, [int? index]) {
    String path = "";
    String folderName1 = "";
    String folderName2 = "";
    if (isCloud) {
      folderName1 = "Clould_Template";
      folderName2 = "Clould_ThumbBack";
    } else {
      folderName1 = "Template";
      folderName2 = "ThumbBack";
    }
    if (index != null) {
      path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_${index}_zh-cn.png';
    } else {
      path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_zh-cn.png';
    }
    if (File(path).existsSync()) {
      debugPrint("迁移:背景图存在path: $path");
      return path;
    }
    if (index != null) {
      path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_${index}_en.png';
    } else {
      path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_en.png';
    }
    if (File(path).existsSync()) {
      debugPrint("迁移:背景图存在path: $path");
      return path;
    }
    if ((profile.extrain.labelId ?? "").isNotEmpty) {
      if (profile.extrain.labelId == imageId) {
        return "";
      } else {
        path = _getBackgroundImagePath(documentsPath, profile.extrain.labelId!, true);
        return path;
      }
    }
    return path;
  }

  List<String> _getRotatedImagePaths(String documentsPath, String imageId, bool isCloud) {
    List<String> rotatedPaths = [];
    List<String> backgroundImages = backgroundImage.split(',');
    String folderName1 = "";
    String folderName2 = "";
    String path = "";
    if (isCloud) {
      folderName1 = "Clould_Template";
      folderName2 = "Clould_ThumbBack";
    } else {
      folderName1 = "Template";
      folderName2 = "ThumbBack";
    }
    if (backgroundImages.length > 1) {
      for (int i = 0; i < backgroundImages.length; i++) {
        path =
            '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_${i}_${canvasRotate}_zh-cn.png';
        rotatedPaths.add(path);
      }
    } else {
      path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${imageId}_back_${canvasRotate}_zh-cn.png';
      rotatedPaths.add(path);
    }
    return rotatedPaths;
  }

  String _getElementImagePath(String documentsPath, String elementId, bool isCloud) {
    String folderName1 = "";
    String folderName2 = "";
    String path = "";
    if (isCloud) {
      folderName1 = "Clould_Template";
      folderName2 = "Clould_Element";
    } else {
      folderName1 = "Template";
      folderName2 = "Element";
    }
    path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${elementId}_zh-cn.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:元素图存在path: $path");
      return path;
    }
    path = '$documentsPath/JCPrintCache/Image/$folderName1/$folderName2/${elementId}_en.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:元素图存在path: $path");
      return path;
    }
    if (!isCloud) {
      debugPrint("迁移:元素图不存在path: $path 云模板文件夹中重新获取");
      path = _getBackgroundImagePath(documentsPath, elementId, true);
      return path;
    }
    return "";
  }

  String _getNinePatchImagePath(String documentsPath, String elementId) {
    String path = "";
    path = '$documentsPath/JCPrintCache/Image/Template/Element/ninePatch/${elementId}_zh-cn.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:九宫格图存在path: $path");
      return path;
    }
    path = '$documentsPath/JCPrintCache/Image/Template/Element/ninePatch/${elementId}_en.png';
    if (File(path).existsSync()) {
      debugPrint("迁移:九宫格图存在path: $path");
      return path;
    }
    return "";
  }
}
