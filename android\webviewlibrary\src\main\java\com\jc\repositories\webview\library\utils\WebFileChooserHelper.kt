package com.jc.repositories.webview.library.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.appframework_library.dialog.SheetDialog
import androidx.core.content.FileProvider
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date

/**
 * WebView 文件选择辅助工具，兼容 Activity/Fragment，回调方式。
 */
object WebFileChooserHelper {
    interface Host {
        val hostContext: Context
        fun hostActivity(): Activity
        fun startActivityForResult(intent: Intent, requestCode: Int)
        fun getFileProviderAuthority(): String
        fun createImageFile(): File?
        fun createVideoFile(): File?
    }

    fun openFileChooseProcess(
        host: Host,
        fileChooserParams: WebChromeClient.FileChooserParams?,
        filePathCallback: ValueCallback<Array<Uri>>?,
        FILE_CHOOSER_REQUEST_CODE: Int,
        onCameraPhotoPath: (String?) -> Unit,
        onCameraVideoPath: (String?) -> Unit
    ) {
        val acceptTypes = fileChooserParams?.acceptTypes ?: emptyArray()
        if (acceptTypes.isNotEmpty()) {
            val acceptType = acceptTypes[0]
            if (acceptType == "video/*" || acceptType == "image/*") {
                requestCameraPermission(host, filePathCallback) { granted ->
                    if (!granted) {
                        filePathCallback?.onReceiveValue(null)
                        return@requestCameraPermission
                    }
                    val captureEnabled = fileChooserParams?.isCaptureEnabled ?: false
                    val capturePhoto = captureEnabled && acceptTypes.contains("image/*")
                    val captureVideo = captureEnabled && acceptTypes.contains("video/*")
                    when {
                        captureEnabled && capturePhoto -> {
                            openCameraChooser(host, filePathCallback, FILE_CHOOSER_REQUEST_CODE, onCameraPhotoPath)
                        }
                        captureEnabled && captureVideo -> {
                            openVideoChooser(host, filePathCallback, FILE_CHOOSER_REQUEST_CODE, onCameraVideoPath)
                        }
                        else -> {
                            openImageChooser(host, filePathCallback, FILE_CHOOSER_REQUEST_CODE, onCameraPhotoPath)
                        }
                    }
                }
                return
            }
        }
        openFileChooser(host, filePathCallback, FILE_CHOOSER_REQUEST_CODE)
    }

    private fun requestCameraPermission(
        host: Host,
        filePathCallback: ValueCallback<Array<Uri>>?,
        callback: (Boolean) -> Unit
    ) {
        PermissionDialogUtils.showCameraPermissionDialog(
            host.hostActivity(),
            RequestCode.MORE,
            object : XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    callback.invoke(true)
                }
                override fun onPermissionDenied(deniedPermissions: Array<String>, alwaysDenied: Boolean) {
                    filePathCallback?.onReceiveValue(null)
                    showToast("app01309")
                    callback.invoke(false)
                }
            })
    }

    fun openImageChooser(
        host: Host,
        filePathCallback: ValueCallback<Array<Uri>>?,
        FILE_CHOOSER_REQUEST_CODE: Int,
        onCameraPhotoPath: (String?) -> Unit
    ) {
        val builder = SheetDialog.Builder(host.hostActivity())
        builder.setContent("app00113", "app00114")
        builder.setBottomName("app00030")
            .setBottomClickLister { _: Any, holder: Any ->
                filePathCallback?.onReceiveValue(null)
                (holder as? java.io.Closeable)?.close()
            }
            .setOnItemClickLister { _: Any, _: Any, position: Int, holder: Any ->
                when (position) {
                    0 -> {
                        (holder as? java.io.Closeable)?.close()
                        var takePictureIntent: Intent? = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                        if (takePictureIntent!!.resolveActivity(host.hostContext.packageManager) != null) {
                            var photoFile: File? = null
                            try {
                                photoFile = host.createImageFile()
                                onCameraPhotoPath("file:" + photoFile?.absolutePath)
                                takePictureIntent.putExtra("PhotoPath", "file:" + photoFile?.absolutePath)
                            } catch (ex: IOException) {
                                ex.printStackTrace()
                            }
                            if (photoFile != null) {
                                val photoURI = FileProvider.getUriForFile(
                                    host.hostActivity(),
                                    host.getFileProviderAuthority(),
                                    photoFile
                                )
                                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                            } else {
                                takePictureIntent = null
                            }
                        }
                        if (takePictureIntent != null)
                            host.startActivityForResult(takePictureIntent, FILE_CHOOSER_REQUEST_CODE)
                    }
                    1 -> {
                        (holder as? java.io.Closeable)?.close()
                        val chooserIntent = Intent(Intent.ACTION_PICK)
                        chooserIntent.type = "image/*"
                        host.startActivityForResult(chooserIntent, FILE_CHOOSER_REQUEST_CODE)
                    }
                }
            }
            .create().show()
    }

    fun openCameraChooser(
        host: Host,
        filePathCallback: ValueCallback<Array<Uri>>?,
        FILE_CHOOSER_REQUEST_CODE: Int,
        onCameraPhotoPath: (String?) -> Unit
    ) {
        var takePictureIntent: Intent? = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (takePictureIntent!!.resolveActivity(host.hostContext.packageManager) != null) {
            var photoFile: File? = null
            try {
                photoFile = host.createImageFile()
                onCameraPhotoPath("file:" + photoFile?.absolutePath)
                takePictureIntent.putExtra("PhotoPath", "file:" + photoFile?.absolutePath)
            } catch (ex: IOException) {
                ex.printStackTrace()
            }
            if (photoFile != null) {
                val photoURI = FileProvider.getUriForFile(
                    host.hostActivity(),
                    host.getFileProviderAuthority(),
                    photoFile
                )
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
            } else {
                takePictureIntent = null
            }
        }
        if (takePictureIntent != null)
            host.startActivityForResult(takePictureIntent, FILE_CHOOSER_REQUEST_CODE)
    }

    fun openVideoChooser(
        host: Host,
        filePathCallback: ValueCallback<Array<Uri>>?,
        FILE_CHOOSER_REQUEST_CODE: Int,
        onCameraVideoPath: (String?) -> Unit
    ) {
        var takeVideoIntent: Intent? = Intent(MediaStore.ACTION_VIDEO_CAPTURE)
        if (takeVideoIntent!!.resolveActivity(host.hostContext.packageManager) != null) {
            var videoFile: File? = null
            try {
                videoFile = host.createVideoFile()
                onCameraVideoPath("file:" + videoFile?.absolutePath)
                takeVideoIntent.putExtra("VideoPath", "file:" + videoFile?.absolutePath)
            } catch (ex: IOException) {
                ex.printStackTrace()
            }
            if (videoFile != null) {
                val videoURI = FileProvider.getUriForFile(
                    host.hostActivity(),
                    host.getFileProviderAuthority(),
                    videoFile
                )
                takeVideoIntent.putExtra(MediaStore.EXTRA_OUTPUT, videoURI)
            } else {
                takeVideoIntent = null
            }
        }
        if (takeVideoIntent != null)
            host.startActivityForResult(takeVideoIntent, FILE_CHOOSER_REQUEST_CODE)
    }

    fun openFileChooser(
        host: Host,
        filePathCallback: ValueCallback<Array<Uri>>?,
        FILE_CHOOSER_REQUEST_CODE: Int
    ) {
        val takePictureIntent = Intent(Intent.ACTION_GET_CONTENT)
        takePictureIntent.type = "*/*"
        host.startActivityForResult(takePictureIntent, FILE_CHOOSER_REQUEST_CODE)
    }
} 