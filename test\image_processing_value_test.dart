import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';

void main() {
  group('ImageElement._parseImageProcessingValue', () {
    test('should handle null values', () {
      expect(ImageElement._parseImageProcessingValue(null), equals([]));
    });

    test('should handle empty string', () {
      expect(ImageElement._parseImageProcessingValue(''), equals([]));
    });

    test('should handle single number string', () {
      expect(ImageElement._parseImageProcessingValue('127'), equals([127]));
      expect(ImageElement._parseImageProcessingValue('255'), equals([255]));
      expect(ImageElement._parseImageProcessingValue('0'), equals([0]));
    });

    test('should handle comma-separated string', () {
      expect(ImageElement._parseImageProcessingValue('127,255'), equals([127, 255]));
      expect(ImageElement._parseImageProcessingValue('100, 200, 300'), equals([100, 200, 300]));
      expect(ImageElement._parseImageProcessingValue('1,2,3,4,5'), equals([1, 2, 3, 4, 5]));
    });

    test('should handle string with brackets', () {
      expect(ImageElement._parseImageProcessingValue('[127]'), equals([127]));
      expect(ImageElement._parseImageProcessingValue('[127,255]'), equals([127, 255]));
      expect(ImageElement._parseImageProcessingValue('[100, 200, 300]'), equals([100, 200, 300]));
      expect(ImageElement._parseImageProcessingValue('[]'), equals([]));
    });

    test('should handle string with extra spaces', () {
      expect(ImageElement._parseImageProcessingValue('  127  '), equals([127]));
      expect(ImageElement._parseImageProcessingValue(' 127 , 255 '), equals([127, 255]));
      expect(ImageElement._parseImageProcessingValue('[ 127 , 255 ]'), equals([127, 255]));
    });

    test('should handle List<int>', () {
      expect(ImageElement._parseImageProcessingValue([127, 255]), equals([127, 255]));
      expect(ImageElement._parseImageProcessingValue([]), equals([]));
    });

    test('should handle List<dynamic>', () {
      expect(ImageElement._parseImageProcessingValue([127, 255]), equals([127, 255]));
    });

    test('should handle single int', () {
      expect(ImageElement._parseImageProcessingValue(127), equals([127]));
    });

    test('should handle invalid string formats', () {
      expect(ImageElement._parseImageProcessingValue('abc'), equals([]));
      expect(ImageElement._parseImageProcessingValue('127,abc'), equals([]));
      expect(ImageElement._parseImageProcessingValue('[abc]'), equals([]));
    });

    test('should handle mixed valid and invalid parts', () {
      // 当有无效部分时，整个解析会失败，返回空数组
      expect(ImageElement._parseImageProcessingValue('127,abc,255'), equals([]));
    });

    test('should handle string with empty parts', () {
      expect(ImageElement._parseImageProcessingValue('127,,255'), equals([127, 255]));
      expect(ImageElement._parseImageProcessingValue(',127,255,'), equals([127, 255]));
    });
  });
}
