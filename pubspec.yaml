name: text
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: '^3.3.0'
  flutter: ">=3.24.0"
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5

  niim_login:
#    path: '../niimprintmodule/niim_login'
    git:
      url: https://git.jc-ai.cn/architect/ios/niimprintmodule.git
      path: niim_login
      ref: 'feature/6_3_3_online'
  image_gallery_saver: '^2.0.3'
  fluttertoast: ^8.2.5
  flutter_easyloading: ^3.0.3
  dio: ^5.0.0
  common_utils: ^2.0.2
  shared_preferences: ^2.0.15
  flutter_slidable: ^3.0.1
  # connectivity: ^3.0.6
  url_launcher: ^6.1.14
  sqflite: ^2.4.1
  video_player: ^2.5.0
  video_player_android: 2.3.11
  chewie: 1.8.5
  keyboard_actions: 4.1.0
  package_info: ^2.0.2
  sprintf: ^7.0.0
  easy_localization: ^3.0.0
  lifecycle: ^0.8.0
  fluro: ^2.0.3
  lottie: ^2.3.2
  connectivity_plus: ^3.0.6
  native_flutter_proxy: ^0.1.15
  flutter_easyrefresh: ^2.2.1
  flutter_facebook_auth: ^5.0.0


  # niimbot_log_plugin:
  #   # path: '../niimbot_log_plugin'
  #   git:
  #     url: 'https://git.jc-ai.cn/print/foundation/niimbot_log_plugin.git'
  #     ref: '1.0.4.0'

  get: 4.6.6
  flutter_svg: ^2.0.9
  pull_to_refresh: ^2.0.0
  sliding_up_panel: ^2.0.0+1
  flutter_staggered_grid_view: ^0.6.2
#  wakelock: ^0.6.2
  measure_size: ^4.0.0
  flutter_localizations:
    sdk: flutter
  rounded_loading_button_plus: ^3.0.1
  flutter_keyboard_visibility: ^6.0.0
  path_provider: ^2.0.6
  crypto: ^3.0.3
  popover:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/popover.git'
      ref: 'feature/0.2.9'
  nimbot_state_manager:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/nimbot_state_manager.git'
      ref: 'feature/flutter_update'
  niimbot_dio_http_cache:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/niimbot_dio_http_cache.git'
      ref: 'main'

  # 画板组件
  niimbot_flutter_canvas:
    path: 'plugins/niimbot_flutter_canvas'

  niimbot_tiny_canvaskit:
    path: 'plugins/niimbot_tiny_canvaskit'
  niimbot_print_setting_plugin:
    path: 'plugins/niimbot_print_setting_plugin'
  # Flutter 画板扩展插件接口定义
  flutter_canvas_plugins_interface:
    path: 'plugins/flutter_canvas_plugins_interface'
#    git:
#      url: https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_canvas_plugins_interface.git
#      ref: 'master'

  wechat_assets_picker: 8.4.4
  # niimbot_print_strategy:
  #   path: '../niimbot_print_strategy'
  niimbot_print_strategy:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_print_strategy.git
      ref: '5c8c4ec1884ce72ff3bb357bf451b39c2afb08ac'

  wechat_camera_picker: ^4.2.1
  # 图像库插件
#  netal_plugin: 1.2.32
  netal_plugin:
    git:
      url: 'https://git.jc-ai.cn/architect/dboard/netal_plugin.git'
      ref: 'c7b05b19767ee686591d9c3ea27248a5da4a07b5'
#      ref: 'feature/black_red_render'
  flutter_net_signature: 0.0.9
  show_fps: ^1.0.6
  smooth_page_indicator: ^1.2.0+3
  enum_to_string: ^2.0.1

  # 模版文件解析与转换图像生成、打印生成“桥”
  niimbot_template:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_template.git
      ref: 'feature/app_templateManager'
#      path: '../plugins/niimbot_template'
#  niimbot_template:
#    path: '../plugins/niimbot_template'
  # 元组
  tuple: ^2.0.2

  # 虚线边框
  dotted_border: ^2.1.0

  # 震动
  vibration: ^1.7.7

  # 富文本编辑器
  flutter_quill: ^9.5.7

  # 富文本编辑器拓展
  # flutter_quill_extensions: ^10.0.0

  pointycastle: ^3.9.1

  # isar数据库
  isar_generator: ^3.1.0+1
  build_runner: ^2.4.9

  # 缓存管理器
  niimbot_cache_manager:
#    path: '../plugins/niimbot_cache_manager'
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_cache_manager.git
      ref: 'feature/6_3_3'
  # 相册选择
  image_picker: ^1.0.7
  image_picker_android: ^0.8.7
  sentry_flutter: 8.14.1

  niimbot_mobile_ui: 1.0.5
#  niimbot_mobile_ui:
#    path: 'plugins/niimbot_mobile_ui'

  niimbot_lego: 1.6.8

  pool: 1.5.1

dependency_overrides:
  flutter_boost:
    git:
      url: 'https://git.jc-ai.cn/architect/flutter/flutter_boost.git'
      ref: 'v5.0.2'
  niimbot_lego: 1.6.8
  netal_plugin:
    git:
      url: 'https://git.jc-ai.cn/architect/dboard/netal_plugin.git'
      ref: 'c7b05b19767ee686591d9c3ea27248a5da4a07b5'
  niimbot_template:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_template.git
      ref: '9be7de157177ed1360c1bc742fc3d8e43b13ca20' ##feature/app_templateManager 合并时间需求至模板管理分支
  # web_socket_channel: 2.2.0
  camera_android: 0.10.7
  niimbot_intl: 1.2.6
  # for dio_http_cache
  json_serializable: ^6.2.0
  wechat_picker_library: 1.0.4
  google_sign_in_ios: 5.8.1
  #json_annotation: ^4.5.0
  ffi: 2.0.2
  photo_manager: 3.2.1
  wechat_camera_picker: ^4.2.1
  http: ^1.0.0
  flutter_line_sdk: 2.3.4
  device_info_plus: ^10.1.0
  wakelock_plus: 1.3.2
  package_info_plus: 4.0.2
  photo_view: ^0.15.0
  meta: 1.12.0
  web: ^1.0.0
  synchronized: 3.0.0
  win32: 5.5.1
  niimbot_excel: 1.1.15
  # niimbot_excel:
  #   path: '../niimbot_excel'
  test_api: 0.7.0
  flutter_inappwebview: ^6.1.5
  niimbot_log_plugin:
#    path: '../niimbot_log_plugin'
    git:
      url: 'https://git.jc-ai.cn/print/foundation/niimbot_log_plugin.git'
      ref: '1.1.0.2'
#  flutter_inappwebview:
#    git:
#      url: 'https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_inappwebview.git'
#      path: 'flutter_inappwebview/'
#      ref: 'xcode_16_fix'
  gql_dio_link: ^1.0.1+1
  extended_image: ^9.1.0
  image_picker_ios: 1.0.3
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  pigeon: ^17.1.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/config/
    - assets/images/
    - assets/images/industry_template/home/
    - assets/images/industry_template/replace_label/
    - assets/images/industry_template/search_template/
    - assets/images/label_create/
    - assets/images/e_tag/
    - assets/images/C1/
    - assets/images/my_template/
    - assets/images/goods_lib/
    - assets/images/profile/
    - assets/images/print_history/
    - assets/images/printerImage/
    - assets/images/canvas/
    - assets/images/type_setting/
    - assets/images/scan/
    - assets/images/try_out_vip/
    - assets/images/my_account/
    - assets/images/message/
    - assets/translation/
    - assets/lottie/
    - assets/translation/
    - assets/

  fonts:
    - family: emoji
      fonts:
        - asset: assets/fonts/emoji.ttf
  #    - assets/translation/login/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  #
  #           style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
