package com.jc.repositories.webview.library.view

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.text.TextUtils
import android.util.Base64
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import com.andsync.xpermission.XPermissionUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import com.jc.repositories.webview.library.NiimbotConstant
import com.jc.repositories.webview.library.R
import com.jc.repositories.webview.library.databinding.FragmentLabelCustomizeBinding
import com.jc.repositories.webview.library.eventbus.AppLifecycleEvent
import com.jc.repositories.webview.library.eventbus.RefreshShopTokenEvent
import com.jc.repositories.webview.library.eventbus.ScanExchangeSuccessEvent
import com.jc.repositories.webview.library.eventbus.ShopStoreBeanEvent
import com.jc.repositories.webview.library.eventbus.ShowTabEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.utils.GlideEngine
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType.ofImage
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.BaseRootActivity
import com.niimbot.appframework_library.messagebus.config.LeIntentConfig
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.template.SearchScanActivityConfig
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.PictureUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.fastjson.JSONObject
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.utiliylibray.util.*
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.baselibrary.base.BasePagerFragment
import melon.south.com.baselibrary.eventbus.CloudCountChangeEvent
import melon.south.com.baselibrary.util.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import com.jc.repositories.webview.library.utils.WebFileChooserHelper

class LabelCustomizeFragment : BasePagerFragment(), WebFileChooserHelper.Host {

    companion object{
        const val TAG = "LabelCustomizeFragment"
    }
    private var url: String = ""
    private var banner: String? = null
    private var webView : LollipopFixedWebView?= null
    private var mView : View ?= null
    private var hasSetShopToken = false

    private lateinit var binding: FragmentLabelCustomizeBinding
    private var scanType = 1
    var pageSource = "y_page_inner_app"
    private var filePathCallback: ValueCallback<Array<Uri>>? = null
    private var cameraPhotoPath: String? = null
    private var cameraVideoPath: String? = null
    private val FILE_CHOOSER_REQUEST_CODE = 121

    override fun getFragmentTag(): String {
        return "LabelCustomizeFragment"
    }

    override fun getLayoutId() = R.layout.fragment_label_customize
    fun setBannerUrl(bannerUrl: String) {
        banner = ShopManager.getFinalUrl(bannerUrl)
        LogUtils.d("setBannerUrl banner= ", banner)
        logE("url", "setBannerUrl: $url")
        banner?.let {
            if (it.isNotEmpty()) {
                webView?.loadUrl(it)
            }
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun init(view: View) {
        this.mView = view
        binding = FragmentLabelCustomizeBinding.bind(view)
        init()
        EventBus.getDefault().register(this)
    }

    private fun getStatusBarHeight(): Int {
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        return resources.getDimensionPixelSize(resourceId)
    }

    private fun initView(){
        webView = this.mView?.findViewById(R.id.webView)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webView?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        } else {
            webView?.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        }
        resetIndex()
        if (TextHookUtil.getInstance().isChina()) {
            //切换语言会因为webView 加载页面后导致状态栏颜色变更
            val layoutParams = webView?.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.setMargins(0, 0, 0, 0)
//            layoutParams.setMargins(0, getStatusBarHeight(), 0, 0)
            webView?.layoutParams = layoutParams
            com.niimbot.baselibrary.user.LoginDataEnum.registerChange(this)
            if (isVisible) {
                showIndexOrBanner()
            }
        }
    }

    private fun init(){
        initView()
        val webSettings = webView?.settings
        webSettings?.javaScriptEnabled = true
        webSettings?.javaScriptCanOpenWindowsAutomatically = true
        webSettings?.cacheMode = WebSettings.LOAD_DEFAULT
        webView?.settings?.domStorageEnabled = true
        webView?.settings?.databaseEnabled = true
        webView?.settings?.javaScriptEnabled = true

        val originUA = webView?.settings?.userAgentString
        webView?.settings?.userAgentString = originUA + " " + SystemUtil.getUserAgent(activity)
        var userAgentStr = webView?.settings?.userAgentString
        LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")

        webView?.webChromeClient = this.MyWebChromeClient()
        webView?.webViewClient = this.MyWebViewClient()
        // JS调用Android的方法
        activity?.let {
            webView?.addJavascriptInterface(LabelScriptinterface(), "android")
            webView?.loadUrl(url)
        }

        try{
            activity?.window?.let { KeyboardUtils.fixAndroidBug5497(it) }
        } catch(e: Exception) {e.printStackTrace()}
    }

    private fun reInit(){
        initView()
        val webSettings = webView?.settings
        webSettings?.javaScriptEnabled = true
        webSettings?.javaScriptCanOpenWindowsAutomatically = true
        webSettings?.cacheMode = WebSettings.LOAD_DEFAULT
        webView?.settings?.domStorageEnabled = true
        webView?.settings?.databaseEnabled = true
        webView?.settings?.javaScriptEnabled = true

        val originUA = webView?.settings?.userAgentString
        webView?.settings?.userAgentString = originUA + " " + SystemUtil.getUserAgent(activity)
        var userAgentStr = webView?.settings?.userAgentString
        LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")

        webView?.webChromeClient = this.MyWebChromeClient()
        webView?.webViewClient = this.MyWebViewClient()
        activity?.let {
            webView?.addJavascriptInterface(LabelScriptinterface(), "android")
            webView?.loadUrl(appendUrl())
        }
    }


    private fun showIndexOrBanner() {
        if (banner.isNullOrEmpty()) {
            webView?.loadUrl(url)
        } else {
            webView?.loadUrl(banner!!)
        }
        webView?.clearHistory()
        banner = null
        resetIndex()
    }

    override fun onLoginStatusChange() {
        logI(TAG, "onLoginStatusChange()")
        resetIndex()
        if(!LoginDataEnum.isLogin){
            if(hasSetShopToken){
//                val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
                val deviceId = BuriedHelper.getEventAnonymousId()
                val platform = "Android_YDY"
                webView?.evaluateJavascript("javascript:AppSetToken('', '${deviceId}', '', '${platform}')", null)
                hasSetShopToken = false
            }
        }
    }

    private fun appendUrl(): String {
        var url = "${ShopManager.LABEL_CUSTOMIZE}?"
        if (!TextUtils.isEmpty(com.niimbot.baselibrary.user.LoginDataEnum.token) && "" != com.niimbot.baselibrary.user.LoginDataEnum.token) {
            url += "token=${HttpTokenUtils.getShopToken()}&"
        }
        url += "version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&preloading=1&back_app=1&entrance_type_id=1&jumpSource=$pageSource"
        url = ShopManager.getFinalUrl(url)
        logD("appendUrl= ", url)
        return url
    }

    private fun resetIndex() {
        url = "${ShopManager.LABEL_CUSTOMIZE}?token=${HttpTokenUtils.getShopToken()}&version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&back_app=1&entrance_type_id=1&from=yundayin&jumpSource=$pageSource"
        url = ShopManager.getFinalUrl(url)
        logI(TAG, "resetIndex: shopHome = ${ShopManager.shopHome}")
        logI(TAG, "resetIndex: url = $url")
    }


    private fun doLoading(newProgress: Int){
        if(binding.progressBar != null) {
            if (newProgress >= 100) {
                binding.progressBar.visibility = View.GONE
                webView?.visibility = View.VISIBLE
            } else {
                binding.progressBar.visibility = View.VISIBLE
                binding.progressBar.progress = newProgress
            }
        }
    }

    private fun openFileChooseProcess(fileChooserParams: WebChromeClient.FileChooserParams?) {
        WebFileChooserHelper.openFileChooseProcess(
            host = this,
            fileChooserParams = fileChooserParams, // 该场景下没有WebChromeClient.FileChooserParams
            filePathCallback = filePathCallback,
            FILE_CHOOSER_REQUEST_CODE = FILE_CHOOSER_REQUEST_CODE,
            onCameraPhotoPath = { path -> cameraPhotoPath = path },
            onCameraVideoPath = { path -> cameraVideoPath = path }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == FILE_CHOOSER_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            var results: Array<Uri>? = null
            if (data == null || data.data == null) {
                if (cameraPhotoPath != null) {
                    results = arrayOf(Uri.parse(cameraPhotoPath))
                }
            } else {
                val dataString: String? = data.dataString
                if (dataString != null) {
                    results = arrayOf(Uri.parse(dataString))
                }
            }
            filePathCallback?.onReceiveValue(results)
            filePathCallback = null
        } else {
            filePathCallback?.onReceiveValue(null)
            filePathCallback = null
        }
        super.onActivityResult(requestCode, resultCode, data)
    }


    override fun onDestroy() {
        webView?.removeAllViews()
        webView?.destroy()
        webView = null
        super.onDestroy()
        LoginDataEnum.unregisterChange(this)
        EventBus.getDefault().unregister(this)
    }

    fun onBackPressed(): Boolean{
        return if (canGoBack() && webView?.canGoBack() == true) {
            webView?.loadUrl("javascript:navigateBack()")
            true
        } else {
            webView?.goBack()
            false
        }
    }

    private fun canGoBack() : Boolean{
        val originalUrl = webView?.copyBackForwardList()?.currentItem?.originalUrl
        if (originalUrl?.contains(ShopManager.SHOP_ROOT.replace("https://", "").replace("http://", "")) == true) {
           return  true
        }
        return  false
    }

    override fun loginStatusChange(isLogin: Boolean) {
        super.loginStatusChange(isLogin)
        LogUtils.d("loginStatusChange isLogin= $isLogin , isVisible = $isVisible")
        if (isLogin && !activity?.isFinishing!! && this.mView != null) {
            reInit()
            if(!AppDataUtil.shopIsService){
                resetIndex()
            }
            url += "&preloading=1"
            LogUtils.d("resetIndex preload= ", url)
            logE("url", "loginStatusChange: $url")
            webView?.loadUrl(url)
            initEvent(this.mView!!)
        }
    }

    fun appUserClickEvent(){
        val params = "javascript:appUserClickEvent()"
        webView?.loadUrl(params)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: AppLifecycleEvent) {
        val background = event.background
        if(background) {
            webView?.evaluateJavascript("javascript:applicationDidEnterBackground()", null)
        }
        else {
            webView?.evaluateJavascript("javascript:applicationWillEnterForeground()", null)
        }
    }


    inner class LabelScriptinterface constructor() {
        @JavascriptInterface
        fun loginOnToApp() {
            activity?.let {
                LoginDataEnum.forceLogin(it) {
                    EventBus.getDefault().post(RefreshShopTokenEvent())
                }
            }
        }
        @JavascriptInterface
        fun refreshUserInfo() {
            EventBus.getDefault().post(ScanExchangeSuccessEvent())
        }
        @JavascriptInterface
        fun backHome(result: String) {
           activity?.finish()
        }

        @JavascriptInterface
        fun jcdy_StatusBar_Color(data: String) {
            if (!AppDataUtil.isStoreFragmentShow) {
                return
            }
            /**
             * 0: 深色（文字白色）
             * 1：浅色（文字黑色）
             */
            //val bool = (activity as MainActivity).mIndex == 1
            // LogUtils.e("jcdy_StatusBar_Color bool= $bool")
            LogUtils.e("jcdy_StatusBar_Color data = $data    ${AppDataUtil.isStoreFragmentShow}")
            GlobalScope.launch(Dispatchers.Main) {
                try {
                    val bean = try{
                        GsonUtils.fromJson(data, NewStoreFragment.StatusBean::class.java)
                    } catch(e: Exception) {
                        e.printStackTrace()
                        NewStoreFragment.StatusBean()
                    } ?: NewStoreFragment.StatusBean()
                    // (activity as MainActivity).mStatusBarColorCache = bean
                    EventBus.getDefault().post(ShopStoreBeanEvent(bean));
                    activity?.let {
                        AppUtils.setStatusBarLightColor(
                            it,
                            Color.parseColor(bean.color),
                            bean.type == "1"
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        @JavascriptInterface
        fun jcdy_shopScan() {
            LogUtils.e("jcdy_shopScan")
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {

                    LeMessageManager.getInstance().dispatchMessage(
                        LeMessage(
                            LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                            SearchScanActivityConfig(activity).apply {
                                setIntentFlag(LeIntentConfig.IntentFlag.START_ACTIVITY_FOR_RESULT)
                                intent.putExtra(com.niimbot.baselibrary.Constant.SCAN_CREATE, true)
                                intent.putExtra("trackSource", 5)
                                intent.putExtra(
                                    NiimbotConstant.TAG_TITLE,
                                    LanguageUtil.checkLanguage("app01099")
                                )
                                intent.putExtra(
                                    NiimbotConstant.TAG_CONTENT,
                                    LanguageUtil.checkLanguage("app01098")
                                )
                                setRequestCode(222) { resultCode, intent ->
                                    if (resultCode == Activity.RESULT_OK) {
                                        val bundle = intent?.extras
                                        scanType = bundle?.getInt("scan_type") ?: 1
                                        val resultStr =
                                            bundle?.getString(com.niimbot.baselibrary.Constant.newScan_from.RESULT_QRCODE_STRING)
                                        webView?.loadUrl("javascript:returnScanResult('$resultStr')")
                                    }
                                }

                            })
                    )
                } else {
                    showToast("app01139")
                }
            }
        }

        @JavascriptInterface
        fun jcdy_shopScan(str: String) {
            LogUtils.e("jcdy_shopScan")
            jcdy_shopScan()
        }

        @JavascriptInterface
        fun jcdy_Enter_Detail(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            //进入订单详情
            GlobalScope.launch(Dispatchers.Main) {
                //                AppUtils.setSuspendStatusBar(activity)
//                AppUtils.setStatusBarColor(activity, Color.parseColor("#6494BC"))
            }
        }

        @JavascriptInterface
        fun jcdy_Leave_Detail(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            GlobalScope.launch(Dispatchers.Main) {
                //                AppUtils.setStatusBarLightColor(activity, Color.WHITE)
            }
        }

        @JavascriptInterface
        fun jcdy_Show_Tabbar(str: String) {
            LogUtils.e("jcdy_Enter_Detail str = ${str}")
            GlobalScope.launch(Dispatchers.Main) {
                LogUtils.d("onShowTab str = ${str}")
                val flag = try {
                    str.toInt()
                } finally {
                    0
                }
                EventBus.getDefault().post(ShowTabEvent(flag == 1))
            }
        }

        @JavascriptInterface
        fun getUserToken(): String {
            return "token=${
                com.niimbot.baselibrary.user.LoginDataEnum.userModule?.token
                ?: ""}&version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&back_app=1&entrance_type_id=1"
        }


        @JavascriptInterface
        fun getPrinterType(): String{
            val lastDeviceName = BluetoothUtil.getLastDeviceName()
            LogUtils.e("js获取lastDeviceName： $lastDeviceName")
            return lastDeviceName
        }


        @JavascriptInterface
        fun gotoAmazonShop(productPath: String, productWebPath: String) {
//            "com.amazon.mobile.shopping://www.amazon.com/products/${productId}/"
//            "https://www.amazon.com/gp/product/${productId}"
//            "com.amazon.mobile.shopping://www.amazon.com/${productPath}"
//            "https://www.amazon.com/${productWebPath}"
            activity?.let {
                try{
                    var intent = Intent(Intent.ACTION_VIEW)
                    intent.data = Uri.parse(if (OtherAppUtil.isAmazonShopInstalled(it))
                        productPath
                    else
                        productWebPath
                    )
                    it.startActivity(intent)
                } catch(e: Exception) {e.printStackTrace()}
            }
        }

        @JavascriptInterface
        fun nativeFunction(url: String) {
            activity?.let { NiimbotGlobal.routingByScheme(it, url) }
        }

        @JavascriptInterface
        fun tabShopShowNumChanged() {
            EventBus.getDefault().post(CloudCountChangeEvent())
        }

        @JavascriptInterface
        fun scanCodeSearchLabelResult(body: Boolean) {
            var jsonObject = JSONObject()
            jsonObject["action"] = "scanResultByShop"
            jsonObject["result"] = body
            jsonObject["type"] = scanType
            EventBus.getDefault().post(any2Json(jsonObject))
        }

        @JavascriptInterface
        fun getAlbum(compressSizeByte: Int) {
            LogUtils.e("======getAlbum, compressSizeByte = $compressSizeByte")
            if (android.os.Environment.getExternalStorageState() != android.os.Environment.MEDIA_MOUNTED) {
                showToast("app01195")
                return
            }
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {
                    activity?.let { context ->
                        PermissionDialogUtils.showGalleryPermissionDialog(
                            context,
                            RequestCode.MORE,
                            object : com.niimbot.appframework_library.common.util.permission.XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {
                                    PictureSelector.create(context)
                                        .openGallery(ofImage())
                                        .setMaxSelectNum(1)
                                        .setSelectionMode(SelectModeConfig.SINGLE)
                                        .setImageEngine(GlideEngine.createGlideEngine())
                                        .setLanguage(
                                            TextHookUtil.getInstance().getPictureSelectorLanguage()
                                        )
                                        .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                            override fun onResult(result: ArrayList<LocalMedia?>?) {
                                                handleChoosePictureResult(result, compressSizeByte)
                                            }

                                            override fun onCancel() {
                                                LogUtils.e("======getAlbum: onCancel")
                                            }
                                        })
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<String>,
                                    alwaysDenied: Boolean
                                ) {
                                    showToast("app01298")
                                }
                            })
                    }
                }
            }
        }

        @JavascriptInterface
        fun getPhotograph(compressSizeByte: Int){
            LogUtils.e("======getPhotograph, compressSizeByte = $compressSizeByte")
            GlobalScope.launch(Dispatchers.Main) {
                if (NetworkUtils.isConnected()) {
                    activity?.let { context ->
                        PermissionDialogUtils.showCameraPermissionDialog(
                            context,
                            RequestCode.MORE,
                            object : com.niimbot.appframework_library.common.util.permission.XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {
                                    PictureSelector.create(context)
                                        .openCamera(ofImage())
                                        .setLanguage(TextHookUtil.getInstance().getPictureSelectorLanguage())
                                        .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                            override fun onResult(result: ArrayList<LocalMedia?>?) {
                                                handleChoosePictureResult(result, compressSizeByte)
                                            }

                                            override fun onCancel() {
                                                LogUtils.e("======getPhotograph: onCancel")
                                            }
                                        })
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<String>,
                                    alwaysDenied: Boolean
                                ) {
                                    showToast("app01309")
                                }
                            })
                    }
                } else {
                    showToast("app01139")
                }
            }
        }

        @JavascriptInterface
        fun loadingEnd(type: Int){
            GlobalScope.launch(Dispatchers.Main){
                val shopToken = HttpTokenUtils.getShopToken()
//                val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
                val deviceId = BuriedHelper.getEventAnonymousId()
                val userId = LoginDataEnum.id.toString()
                val platform = "Android_YDY"
                webView?.evaluateJavascript("javascript:AppSetToken('${shopToken}', '${deviceId}', '${userId}', '${platform}')", null)
                hasSetShopToken = true
            }
        }

        @JavascriptInterface
        fun getClipboardContent(): String {
            val clipboardManager = activity?.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                ?: return ""
            val data = clipboardManager.primaryClip ?: return ""
            if (data.itemCount > 0) {
                if (data.getItemAt(0) == null || data.getItemAt(0).text == null || TextUtils.isEmpty(
                        data.getItemAt(0).text.toString()
                    )
                ) {
                    return ""
                }
                return data.getItemAt(0).text.toString()
            }
            return ""
        }
    }

    private inner class MyWebChromeClient : WebChromeClient() {

        override fun onConsoleMessage(p0: ConsoleMessage?): Boolean {
            return true
        }

        override fun onJsAlert(p0: WebView?, p1: String?, p2: String?, p3: JsResult?): Boolean {
            return false
        }

        override fun onShowFileChooser(
            webView: WebView?,
            pathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            filePathCallback = pathCallback
            openFileChooseProcess(fileChooserParams)
            return true
        }

        override fun onProgressChanged(p0: WebView?, newProgress: Int) {
            doLoading(newProgress)
        }
    }

    private inner class MyWebViewClient : WebViewClient() {

        override fun onLoadResource(p0: WebView?, pUrl: String?) {
            super.onLoadResource(p0, pUrl)
        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
        }

        override fun onPageFinished(view: WebView?, url: String) {
            super.onPageFinished(view, url)
            if (url == <EMAIL>) {
                webView?.clearHistory()
            }
        }

        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            if (url == null) return false
            return isPay(view, url)
        }

        override fun onReceivedSslError(
            view: WebView?,
            handler: SslErrorHandler?,
            error: SslError?
        ) {
            super.onReceivedSslError(view, handler, error)
            handler?.proceed()
        }
    }

    private fun isPay(view: WebView?, url: String): Boolean {
        LogUtils.e("shouldOverrideUrlLoading()--> Url: $url")
        if (url.contains("weixin://wap/pay?") || url.contains("http://weixin/wap/pay")) {
            try {
                activity?.startActivity(Intent("android.intent.action.VIEW", Uri.parse(url)))
                return true
            } catch (e: Exception) {
                ToastInstance.INSTANCE.showToast("app01193")
            }
        } else if (url.contains("wx.tenpay.com")) {
            var extraHeaders = HashMap<String, String>()
            extraHeaders["Referer"] = ShopManager.WX_PAY
            view?.loadUrl(url, extraHeaders)
            return true
        } else {
            if(url.startsWith("alipays:") || url.startsWith("alipay")) {
                try {
                    activity?.startActivity( Intent ("android.intent.action.VIEW", Uri.parse(url)))
                    return true
                } catch (e: Exception) { }
            } else {
                val tokenSuffix = "?token=${HttpTokenUtils.getShopToken()}"
//                view?.loadUrl(if (url.contains(ShopManager.SHOP_ROOT.replace("http://", "")) && !url.contains(tokenSuffix)) "$url$tokenSuffix" else url)
                view?.loadUrl(url)
                LogUtils.e("--> Url: $url")
            }
        }
        return false
    }

    private fun handleChoosePictureResult(result: ArrayList<LocalMedia?>?, compressSizeByte: Int){
        val compressSize = compressSizeByte / 1024
        val imagePath = result?.getOrNull(0)?.realPath
        if(imagePath.isNullOrEmpty()){
            return
        }
        val compressDir = File(SuperUtils.superContext.filesDir, "store/image_compress/").absolutePath
        GlobalScope.launch(Dispatchers.Main){
            webView?.evaluateJavascript("javascript:imageLoading()", null)
            val base64Data = withContext(Dispatchers.IO){
                val finalImagePath = PictureUtils.compressPictureIfNecessary(imagePath, compressDir, compressSize)
                fileToBase64(finalImagePath)
            }
            // if(base64Data.isNotEmpty()){
            webView?.evaluateJavascript("javascript:imageDataCallback('${base64Data}')", null)
            // }
        }
    }

    /**
     * 将图片转换成Base64编码的字符串
     */
    private fun fileToBase64(path: String): String {
        if (path.isNullOrEmpty() || !File(path).exists()) {
            return ""
        }
        var inputStream: InputStream? = null
        val data: ByteArray
        try {
            inputStream = FileInputStream(path)
            //创建一个字符流大小的数组。
            data = ByteArray(inputStream.available())
            //写入数组
            inputStream.read(data)
            //用默认的编码格式进行编码
            return Base64.encodeToString(data, Base64.NO_WRAP)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close()
                }
                catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return ""
    }

    // WebFileChooserHelper.Host接口实现
    override val hostContext: Context
        get() = requireContext()
    override fun hostActivity(): Activity = requireActivity()
    override fun getFileProviderAuthority(): String = "com.gengcon.android.jccloudprinter.fileProvider"
    override fun createImageFile(): File? = try {
        val timeStamp: String = java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(java.util.Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        requireActivity().getExternalFilesDir(null)?.let {
            File.createTempFile(imageFileName, ".jpg", it)
        }
    } catch (e: IOException) { null }
    override fun createVideoFile(): File? = try {
        val timeStamp: String = java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(java.util.Date())
        val videoFileName = "MP4_" + timeStamp + "_"
        requireActivity().getExternalFilesDir(null)?.let {
            File.createTempFile(videoFileName, ".mp4", it)
        }
    } catch (e: IOException) { null }
    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        super.startActivityForResult(intent, requestCode)
    }
}
