package com.jc.repositories.webview.library.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.webkit.ValueCallback
import android.webkit.WebBackForwardList
import android.webkit.WebChromeClient
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.content.FileProvider
import com.blankj.utilcode.util.*
import com.jc.repositories.webview.library.NiimbotConstant
import com.jc.repositories.webview.library.R
import com.jc.repositories.webview.library.databinding.ActivityNiimbotBaseWebviewBinding
import com.jc.repositories.webview.library.eventbus.AppLifecycleEvent
import com.jc.repositories.webview.library.eventbus.ShopStoreBeanEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.utils.GlideEngine
import com.jc.repositories.webview.library.view.webview.BaseWebChromeClient
import com.jc.repositories.webview.library.view.webview.BaseWebClient
import com.jc.repositories.webview.library.view.webview.JCShopScriptInterface
import com.jc.repositories.webview.library.view.webview.WebPrintHelper
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType.ofImage
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.dialog.DialogFactory
import com.niimbot.appframework_library.dialog.SheetDialog
import com.niimbot.appframework_library.expand.clipRounded
import com.niimbot.appframework_library.expand.dp
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.safeLet
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.toJson
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.messagebus.config.LeIntentConfig
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.template.SearchScanActivityConfig
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.PictureUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.Constant
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.event.LoginChangeEvent
import com.niimbot.baselibrary.network.JCHttpConfig
import com.niimbot.baselibrary.share.ShareHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.fastjson.JSON
import com.niimbot.graphqllibrary.Apollo
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.SystemUtil
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.viplibrary.NiimbotWebPayActivity
import com.niimbot.viplibrary.bean.PriceBean
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.json2Any
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.JCApi
import melon.south.com.baselibrary.base.BaseActivity
import melon.south.com.baselibrary.base.SecondWebViewActivity
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.util.EventBusUtils
import melon.south.com.baselibrary.util.StringUtil
import melon.south.com.baselibrary.util.showToast
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.Date
import com.jc.repositories.webview.library.utils.WebFileChooserHelper


/**
 * @ClassName: NiimbotBaseWebActivity
 * @Author: Liuxiaowen
 * @Date: 2022/1/4 11:55
 * @Description:
 */
open class NiimbotBaseWebActivity: BaseActivity(), LoadProgressListener, WebFileChooserHelper.Host {

    private var title: String? = null
    private var dialogMode = false
    var originUrl: String? = null
    private var machineId: String ?= null
    private var showTitleBar = true
    var wv_base: LollipopFixedWebView? = null
    private var fromAdPage = false
    private var showShareIcon = true
    private var catchBackEvent = false
    private var hasSetShopToken = false
    private var fromMallActivity = false
    private var needShowProgress = true

    val FILE_CHOOSER_REQUEST_CODE = 1
    private var cameraPhotoPath: String? = null
    private var cameraVideoPath: String? = null



    private val binding by viewBinding(ActivityNiimbotBaseWebviewBinding::inflate)
    open fun isShop() = false
    open fun showProgressBar() = true
    fun isFromAppStart() = fromAdPage

    override fun getLayoutId() = R.layout.activity_niimbot_base_webview

    override fun isShowTitleBar(): Boolean {
        return showTitleBar
    }

    override fun getBindingView() = binding.root

    override fun extraIntent() {
        super.extraIntent()
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        originUrl = intent.getStringExtra("url") ?: return
        showTitleBar = intent.getBooleanExtra("showTitleBar", false)
        fromAdPage = intent.getBooleanExtra("fromAdPage", false)
        title = intent.getStringExtra("title")
        dialogMode = intent.getBooleanExtra("dialogMode", false)
        showShareIcon = intent.getBooleanExtra("showShareIcon", true)
        catchBackEvent = intent.getBooleanExtra("catchBackEvent", false)
        fromMallActivity = intent.getBooleanExtra("fromMallActivity", false)
        needShowProgress = intent.getBooleanExtra("showProgressBar", false)
        buildURL()
    }

    override fun init() {
    }
    override fun init2(savedInstanceState: Bundle?) {
        try{
            KeyboardUtils.fixAndroidBug5497(this)
        } catch(e: Exception) {e.printStackTrace()}

        setWebTitle(title ?: "")
        setWebShare()

        binding.webviewContainer.removeAllViews()
        binding.rootLayout.setBackgroundResource(if (dialogMode) R.color.black_30 else R.color.white)
        registerEventBus()


        initWebView(savedInstanceState)
        val params = parseQueryParameters(originUrl!!)
        if(params["fullSceen"] == "1"){
            showTitleBar = false
        }
        if (showTitleBar) {
            binding.webviewContainer.fitsSystemWindows = !showTitleBar
            showAutoCloseLoading(3000)
        } else {
            if(params["fullSceen"] == "1"){
                showAutoCloseLoading(3000)
            }else{
                binding.rootLayout.fitsSystemWindows = true
            }
        }
        binding.progressBar?.visible(showProgressBar()&&needShowProgress)
    }

    fun parseQueryParameters(url: String): Map<String, String> {
        var params = emptyMap<String,String>()
        if(TextUtils.isEmpty(originUrl)){
            return params
        }
        try {
            val uri = Uri.parse(url)
            val queryParameterNames = uri.queryParameterNames
            params = queryParameterNames.associateWith { uri.getQueryParameter(it) ?: "" }
            LogUtils.e("parseQueryParameters $params")
        }catch (e: Exception){
            print(e.printStackTrace())
        }
        return params
    }


    open fun buildURL() {
        val urls = StringBuilder()
        urls.append(originUrl)
        if (needAddToken()) {
            //区别繁体中文和其他
            if (!urls.contains("lang=")) {
//                val languageCode = if (TextHookUtil.getInstance().isChTw) TextHookUtil.LANGUAGE_ZH_TW else TextHookUtil.getInstance().languageName
                val languageCode = TextHookUtil.getInstance().languageName
                urls.append("${if (urls.contains("?")) "&" else "?"}lang=" + languageCode)
            }
//            if (!TextUtils.isEmpty(machineId) && !urls.contains("machineId=")) {
//                urls.append("${if (urls.contains("?")) "&" else "?"}machineId=${BluetoothUtil.getLastDeviceId()}")
//            }
            if (BluetoothUtil.getLastDeviceId().isNotEmpty()) {
                urls.append("${if (urls.contains("?")) "&" else "?"}machineId=${BluetoothUtil.getLastDeviceId()}")
            }
            if (!urls.contains("userAgent=")) {
                urls.append("${if (urls.contains("?")) "&" else "?"}userAgent=${SystemUtil.getUserAgent(this)}")
            }
            if (LoginDataEnum.isLogin) {
                if (!urls.contains("token=")) {
                    if (isShop()) {
                        urls.append("${if (urls.contains("?")) "&" else "?"}token=${HttpTokenUtils.getShopToken()}&version=${AppUtils.getAppVersionName()}&platform_system_id=CP001&back_app=1&entrance_type_id=2")
                    } else {
                        urls.append("${if (urls.contains("?")) "&" else "?"}token=" + HttpTokenUtils.getToken())
                    }
                }
            }
            if (!urls.contains("back_app=1") && isShop()) {
                urls.append("${if (urls.contains("?")) "&" else "?"}back_app=1")
            }
            originUrl = urls.toString()
            if(fromMallActivity){
                originUrl = ShopManager.getMallActivityFinalUrl(originUrl!!)
            }
            else {
                originUrl = ShopManager.getFinalUrl(originUrl!!)
            }

            if (null != originUrl && (originUrl!!.contains(JCApi.HELP_CENTER, true))) {
                BuriedHelper.trackEvent("view", "009")
            }
        }
    }

    private fun needAddToken(): Boolean {
        return !StringUtil.isImageUrl(originUrl)
//                && (originUrl?.contains(JCHttpConfig.WEB_URL, true) == true
//                || originUrl?.contains(JCHttpConfig.SHOP_ROOT, true) == true
//                || originUrl?.contains(JCHttpConfig.HIPPO_URL, true) == true)
    }

    open fun enableKeyBack() = true

    open fun getWebView() {
        wv_base = JCWebViewFactory.getInstance().createWebView(this)
    }


    private fun initWebView(savedInstanceState: Bundle?) {
        getWebView()
        wv_base?.apply {
            if (dialogMode) { clipRounded(SizeUtils.dp2px(12f)) }
            clearHistory()
            // 设置与Js交互的权限
            settings.javaScriptEnabled = true

            val originUA = settings.userAgentString
            if (!originUA.contains("AppId/com.gengcon.android.jccloudprinter", true)) {
                settings.userAgentString = originUA +" "+ SystemUtil.getUserAgent(this@NiimbotBaseWebActivity)
            }
            var userAgentStr = settings.userAgentString
            LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")
            webViewClient = BaseWebClient(this@NiimbotBaseWebActivity)
            webChromeClient = BaseWebChromeClient(this@NiimbotBaseWebActivity)
            addJavascriptInterface(JCShopScriptInterface(), "android")
            if (null != savedInstanceState) {
                this.restoreState(savedInstanceState)
            } else{
                originUrl?.let { loadUrl(it) }
            }

//            setOnKeyListener(object : View.OnKeyListener {
//                override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
//                    if (event?.action == KeyEvent.ACTION_DOWN) {
//                        if (keyCode == KeyEvent.KEYCODE_BACK) {
//                            return if (enableKeyBack()) {
//                                if (wv_base?.canGoBack() == true) {
//                                    if (isShop() && shopCanGoBack()) {
//                                        onShopBackPressed()
//                                    } else {
//                                        <EMAIL>()
//                                    }
//                                    true
//                                } else {
//                                    false
//                                }
//                            } else {
//                                true
//                            }
//                        } else if (keyCode == KeyEvent.KEYCODE_VOLUME_UP || keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
//                            // 2022/7/4 Ice_Liu 播放外链时，音量键造成页面退栈
//                            return wv_base?.canGoBack() == true
//                        }
//                    }
//                    return false
//                }
//            })
            binding.webviewContainer?.addView(
                this@apply, FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                ).apply { setMargins(0, SizeUtils.dp2px(if (dialogMode) 35f else 0f), 0, 0) }
            )
        }
    }

    var filePathCallback: ValueCallback<Array<Uri>>? = null
    fun openFileChooseProcess(fileChooserParams: WebChromeClient.FileChooserParams?) {
        WebFileChooserHelper.openFileChooseProcess(
            host = this,
            fileChooserParams = fileChooserParams,
            filePathCallback = filePathCallback,
            FILE_CHOOSER_REQUEST_CODE = FILE_CHOOSER_REQUEST_CODE,
            onCameraPhotoPath = { path -> cameraPhotoPath = path },
            onCameraVideoPath = { path -> cameraVideoPath = path }
        )
    }


    fun routingByScheme(url: String) {
        NiimbotGlobal.routingByScheme(this, url, sourcePage = "028")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == FILE_CHOOSER_REQUEST_CODE && resultCode == RESULT_OK) {
            var results: Array<Uri>? = null
            if (data == null || data.data == null) {
                if (cameraPhotoPath != null) {
                    results = arrayOf(Uri.parse(cameraPhotoPath))
                }
            } else {
                val dataString: String? = data.dataString
                if (dataString != null) {
                    results = arrayOf(Uri.parse(dataString))
                }
            }
            filePathCallback?.onReceiveValue(results)
            filePathCallback = null
        } else {
            filePathCallback?.onReceiveValue(null)
            filePathCallback = null
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        XPermissionUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onBackPressed() {
        checkH5HookBack()
    }

    private fun checkH5HookBack(){
        wv_base?.evaluateJavascript("typeof navigateBack !== 'undefined'") { value ->
            if ("true" == value) {
                LogUtils.d("==============webHook，navigateBack： true")
                // 安全调用
                wv_base?.evaluateJavascript("javascript:navigateBack()"){ result ->
                    LogUtils.d("==============webHook，invoke ==》navigateBack： $result")
                    if ("true" != result) {
                        goBack("")
                    }
                }
            } else {
                LogUtils.d("==============webHook，navigateBack： false")
                goBack("")
            }
        } ?: goBack("")
    }

    fun goBack(str: String, justCurrent: Boolean = true) {
        if (justCurrent) {
            if (isCurrentFront()) {
                if (fromAdPage) {
                    NiimbotGlobal.gotoHome(this)
                    finish()
                } else {
                    canGoBack()
                }
            }else{
                canGoBack()
            }
        } else {
            canGoBack()
        }
    }

    private fun canGoBack(){
        if (wv_base?.canGoBack() == true) {
            wv_base?.goBack()
        } else {
            finish()
        }
    }

    fun goHardwareDoc(str: String){
        var intent = Intent(mActivity, SecondWebViewActivity::class.java)
        intent.putExtra("url", "${com.niimbot.baselibrary.network.JCHttpConfig.WEB_URL}/h5#/hardwareDoc?imageUrl=${str}")
        intent.putExtra("title", LanguageUtil.findLanguageString("app01130", mActivity))
        startActivity(intent)
    }

    fun goMarket(str: String) {
        if (str.isNotEmpty() && str.startsWith("market:")) {
            startActivity(Intent(Intent.ACTION_VIEW).apply { data = Uri.parse(str) })
        }
    }

    override fun doLoading(progress: Int) {
        if (showProgressBar() && needShowProgress) {
            if (progress >= 100) {
                binding.progressBar?.visibility = View.GONE
                wv_base?.visibility = View.VISIBLE
            } else {
                binding.progressBar?.visibility = View.VISIBLE
                binding.progressBar?.progress = progress
            }
        }
    }

    private fun setWebTitle(webviewTitle: String = "") {
        if (isShop()) {
            mSuperTitleBar?.gone()
        } else {
            mSuperTitleBar?.apply {
                this.getTextView().maxLines = 1
                this.getTextView().ellipsize = TextUtils.TruncateAt.END
                this.getTextView().maxWidth = 200.dp
                visible(showTitleBar)
                (if (title.isNullOrEmpty()) webviewTitle else title)?.let { setText(it) }
                setLeftClickListener {
                    if (!catchBackEvent) {
                        onBackPressed()
                    } else {
                        if (wv_base?.canGoBack() == true) {
                            if (isShop() && shopCanGoBack()) {
                                onShopBackPressed()
                            } else {
                                onWebViewGoBack()
                            }
                        } else onBackPressed()
                    }
                }
            }
        }
    }

    fun setFrameTitle(str: String) {
        freshTitle(str)
    }

    private fun setWebShare() {
        if (!showShareIcon ||
            originUrl?.contains(JCHttpConfig.USER_PRIVACY_URL) == true ||
            originUrl?.contains(JCHttpConfig.VIP_AGREEMENT) == true ||
            originUrl?.contains(JCHttpConfig.AUTO_PAY) == true ||
            originUrl?.contains(JCHttpConfig.WEB_PAY_DETAIL_URL) == true ||
            originUrl?.contains(JCHttpConfig.PRIVACY_URL) == true) {return}

        mSuperTitleBar?.setRightIcon(R.drawable.ic_baseline_more_)
        mSuperTitleBar?.setRightClickListener {
            showShareDialog()
        }
    }

    private fun getWebTitle() {
        val forwardList: WebBackForwardList? = wv_base?.copyBackForwardList()
        val item = forwardList?.currentItem
        if (item != null) {
            setWebTitle(item.title)
        }
    }

    fun freshTitle(title: String) {
        setWebTitle(title)
    }

    private fun onWebViewGoBack() {
        wv_base?.goBack()
        getWebTitle()
    }

    private fun isShitPage(): Boolean {
        return wv_base?.url?.lowercase() =="https://www.niimbot.com/cnmobile/developer_work_order_yundayin.html"
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                if (wv_base?.canGoBack() == true && isShitPage()) {
                    goBack("")
                    return true
                }
                return if (enableKeyBack()) {
                    if (wv_base?.canGoBack() == true) {
                        if (isShop() && shopCanGoBack()) {
                            onShopBackPressed()
                        } else {
                            onWebViewGoBack()
                        }
                        true
                    } else {
                        super.onKeyDown(keyCode, event)
                    }
                } else {
                    true
                }
            } else if (keyCode == KeyEvent.KEYCODE_VOLUME_UP || keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
                val am = SuperUtils.superContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager?
                if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
                    am?.adjustStreamVolume(
                        AudioManager.STREAM_MUSIC,
                        AudioManager.ADJUST_RAISE,
                        AudioManager.FLAG_SHOW_UI
                    );
                } else {
                    am?.adjustStreamVolume(
                        AudioManager.STREAM_MUSIC,
                        AudioManager.ADJUST_LOWER,
                        AudioManager.FLAG_SHOW_UI
                    );
                }
                // 2022/7/4 Ice_Liu 播放外链时，音量键造成页面退栈
                return wv_base?.canGoBack() == true
            }
        }

        return super.onKeyDown(keyCode, event)
    }

    private var shareDialog: DialogFactory.Holder? = null
    private fun showShareDialog() {
        shareDialog?.dismiss()
        shareDialog = DialogFactory.newBuild(this)
            .setLayout(R.layout.dialog_share_web)
            .setWidth(1f)
            .setHeight(-2)
            .setDimAmount(0.5f).setGravity(Gravity.BOTTOM)
            .create()

        arrayOf(shareDialog?.getView(R.id.tv_copy),
            shareDialog?.getView<View>(R.id.iv_copy))?.forEach {
                it?.setOnNotDoubleClickListener {
                    putUrlToClip()
                    shareDialog?.dismiss()
                }
        }
        arrayOf(shareDialog?.getView(R.id.iv_browse),
            shareDialog?.getView<View>(R.id.tv_browse))?.forEach {
                it?.setOnNotDoubleClickListener {
                    openBrowse()
                    shareDialog?.dismiss()
                }
        }
        arrayOf(shareDialog?.getView(R.id.iv_share_wechat),
            shareDialog?.getView<View>(R.id.tv_share_wechat))?.forEach {
                it?.setOnNotDoubleClickListener {
                    getSafeUrl()?.let { it1 -> share2Wechat(it1) }
                    shareDialog?.dismiss()
                }
        }

        shareDialog?.getView<TextView>(R.id.tv_cancel)?.setOnNotDoubleClickListener {
            shareDialog?.dismiss()
        }
        shareDialog?.show()
    }

    private fun getSafeUrl() = wv_base?.url?.let{
//        var shopSuffix = "&token=${URLEncoder.encode(HttpTokenUtils.getShopToken())}".replace("+", "%20")
//        var agentSuffix = "&userAgent=${URLEncoder.encode(SystemUtil.getUserAgent(this))}".replace("+", "%20")
//        var tokenSuffix = "&token=${URLEncoder.encode(HttpTokenUtils.getToken())}".replace("+", "%20")
//        var resultUrl = it.replace(tokenSuffix, "")
//            .replace(agentSuffix, "")
//            .replace(shopSuffix, "")
//
//        resultUrl
        it
    }

    protected fun share2Wechat(shareObj: String) {
        try{
            var json = JSONObject(shareObj)
            val shareUrl = json.optString("shareUrl")
            val title = json.optString("title") ?: ""
            val description = json.optString("description") ?: ""
            val iconUrl = json.optString("icon") ?: ""
            val needBindMainAccount = json.optBoolean("needBindMainAccount")
            if (!shareUrl.isNullOrEmpty() && StringUtil.isUrl(shareUrl)) {
                if (needBindMainAccount) {
                    LoginDataEnum.mainAccountCheck(this){
                        ShareHelper.share2Wechat(this, shareUrl, title, description, iconUrl)
                    }
                } else {
                    ShareHelper.share2Wechat(this, shareUrl, title, description, iconUrl)
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    private fun openBrowse() {
        try{
            getSafeUrl()?.let{
                Apollo.createShorty(it){ shortUrl ->
                    if (!shortUrl.isNullOrEmpty()) NiimbotGlobal.gotoBrowse(this, shortUrl)
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }
    private fun putUrlToClip() {
        try{
            getSafeUrl()?.let {
                Apollo.createShorty(it){ shortUrl ->
                    if (!shortUrl.isNullOrEmpty()) {
                        NiimbotGlobal.putTextToClip(this, shortUrl)
                        wv_base?.post { showToast("app100000073") }
                    }
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    override fun onResume() {
        super.onResume()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onEvent(event: LoginChangeEvent) {
        LogUtils.e("NiimbotBaseWebActivity, LoginChangeEvent--->call onOpenVIP")
        buildURL()
        safeLet(originUrl, wv_base) { url, webView ->
            LogUtils.e("调用appLoginSuccess， token： ${HttpTokenUtils.getToken()}")
            webView.loadUrl("javascript:appLoginSuccess('${HttpTokenUtils.getToken()}')")
            webView.loadUrl("javascript:vipModalCallback('{\"isVip\":\"1\"}')")
            webView.loadUrl("javascript:onOpenVIP()")
        }
        if(!LoginDataEnum.isLogin){
            if(hasSetShopToken){
//                val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
                val deviceId = BuriedHelper.getEventAnonymousId()
                val platform = "Android_YDY"
                wv_base?.evaluateJavascript("javascript:AppSetToken('', '${deviceId}', '', '${platform}')", null)
                hasSetShopToken = false
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: AppLifecycleEvent) {
        val background = event.background
        if(background) {
            wv_base?.evaluateJavascript("javascript:applicationDidEnterBackground()", null)
        }
        else {
            wv_base?.evaluateJavascript("javascript:applicationWillEnterForeground()", null)
        }
    }


    override fun onDestroy() {
        binding.webviewContainer.removeAllViews()
        EventBusUtils.unregister(this)
        wv_base?.let {
            if (!isDestroyed) {
                JCWebViewFactory.getInstance().recycleWebView(it)
                it.loadUrl("about:blank")
                ThreadUtils.runOnUiThreadDelayed({ it.clearHistory() }, 500)
            }
        }
        super.onDestroy()
    }


    fun onShopBackPressed() {
        if (shopCanGoBack() && wv_base?.canGoBack() == true) {
            wv_base?.loadUrl("javascript:navigateBack()")
        } else {
            goBack("")
        }
    }

    fun shopCanGoBack() : Boolean{
        val originalUrl = wv_base?.copyBackForwardList()?.currentItem?.originalUrl
        if (originalUrl?.contains(ShopManager.SHOP_ROOT.replace("https://", "").replace("http://", "")) == true) {
            return  true
        }
        return  false
    }

    fun getAlbum(compressSizeByte: Int) {
        com.southcity.watermelon.util.LogUtils.e("======getAlbum, compressSizeByte = $compressSizeByte")
        if (android.os.Environment.getExternalStorageState() != android.os.Environment.MEDIA_MOUNTED) {
            showToast("app01195")
            return
        }
        GlobalScope.launch(Dispatchers.Main) {
            if (NetworkUtils.isConnected()) {
                PermissionDialogUtils.showGalleryPermissionDialog(
                    this@NiimbotBaseWebActivity,
                    RequestCode.MORE,
                    object : XPermissionUtils.OnPermissionListener {
                        override fun onPermissionGranted() {
                            PictureSelector.create(this@NiimbotBaseWebActivity)
                                .openGallery(ofImage())
                                .setMaxSelectNum(1)
                                .setSelectionMode(SelectModeConfig.SINGLE)
                                .setImageEngine(GlideEngine.createGlideEngine())
                                .setLanguage(
                                    TextHookUtil.getInstance().getPictureSelectorLanguage()
                                )
                                .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                    override fun onResult(result: ArrayList<LocalMedia?>?) {
                                        handleChoosePictureResult(result, compressSizeByte)
                                    }

                                    override fun onCancel() {
                                        com.southcity.watermelon.util.LogUtils.e("======getAlbum: onCancel")
                                    }
                                })
                        }

                        override fun onPermissionDenied(
                            deniedPermissions: Array<String>,
                            alwaysDenied: Boolean
                        ) {
                            showToast("app01298")
                        }
                    })
            }
        }
    }

    fun getPhotograph(compressSizeByte: Int){
        com.southcity.watermelon.util.LogUtils.e("======getPhotograph, compressSizeByte = $compressSizeByte")
        GlobalScope.launch(Dispatchers.Main) {
            if (NetworkUtils.isConnected()) {
                PermissionDialogUtils.showCameraPermissionDialog(
                    this@NiimbotBaseWebActivity,
                    RequestCode.MORE,
                    object : XPermissionUtils.OnPermissionListener {
                        override fun onPermissionGranted() {
                            PictureSelector.create(this@NiimbotBaseWebActivity)
                                .openCamera(ofImage())
                                .setLanguage(TextHookUtil.getInstance().getPictureSelectorLanguage())
                                .forResult(object : OnResultCallbackListener<LocalMedia?> {
                                    override fun onResult(result: ArrayList<LocalMedia?>?) {
                                        handleChoosePictureResult(result, compressSizeByte)
                                    }

                                    override fun onCancel() {
                                        com.blankj.utilcode.util.LogUtils.e("======getPhotograph: onCancel")
                                    }
                                })
                        }

                        override fun onPermissionDenied(
                            deniedPermissions: Array<String>,
                            alwaysDenied: Boolean
                        ) {
                            showToast("app01309")
                        }
                    })
            } else {
                showToast("app01139")
            }
        }
    }

    fun loadingEnd(type: Int){
        GlobalScope.launch(Dispatchers.Main){
            val shopToken = HttpTokenUtils.getShopToken()
//            val deviceId = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
            val deviceId = BuriedHelper.getEventAnonymousId()
            val userId = LoginDataEnum.id.toString()
            val platform = "Android_YDY"
            wv_base?.evaluateJavascript("javascript:AppSetToken('${shopToken}', '${deviceId}', '${userId}', '${platform}')", null)
            hasSetShopToken = true
        }
    }

    private fun handleChoosePictureResult(result: ArrayList<LocalMedia?>?, compressSizeByte: Int){
        val compressSize = compressSizeByte / 1024
        val imagePath = result?.getOrNull(0)?.realPath
        if(imagePath.isNullOrEmpty()){
            return
        }
        val compressDir = File(SuperUtils.superContext.filesDir, "store/image_compress/").absolutePath
        GlobalScope.launch(Dispatchers.Main){
            wv_base?.evaluateJavascript("javascript:imageLoading()", null)
            val base64Data = withContext(Dispatchers.IO){
                val finalImagePath = PictureUtils.compressPictureIfNecessary(imagePath, compressDir, compressSize)
                fileToBase64(finalImagePath)
            }
            // if(base64Data.isNotEmpty()){
            wv_base?.evaluateJavascript("javascript:imageDataCallback('${base64Data}')", null)
            // }
        }
    }

    /**
     * 将图片转换成Base64编码的字符串
     */
    private fun fileToBase64(path: String): String {
        if (path.isNullOrEmpty() || !File(path).exists()) {
            return ""
        }
        var inputStream: InputStream? = null
        val data: ByteArray
        try {
            inputStream = FileInputStream(path)
            //创建一个字符流大小的数组。
            data = ByteArray(inputStream.available())
            //写入数组
            inputStream.read(data)
            //用默认的编码格式进行编码
            return Base64.encodeToString(data, Base64.NO_WRAP)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close()
                }
                catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return ""
    }

    fun startPrint(jsonStr: String) {
        val event = JSON.parseObject(jsonStr)
        event["action"] = WebPrintHelper.TAG_WEB_PRINT
        EventBus.getDefault().post(event.toJSONString())
    }

    fun toConnectPage(jsonStr: String) {
        val event = JSON.parseObject(jsonStr)
        event["action"] = WebPrintHelper.TAG_WEB_PRINTER_CONNECT
        EventBus.getDefault().post(event.toJSONString())
    }

    fun uploadExcel(jsonStr: String) {
        val event = JSON.parseObject(jsonStr)
        event["action"] = WebPrintHelper.TAG_UPLOAD_EXCEL_TO_WEB
        EventBus.getDefault().post(event.toJSONString())
    }

    fun toNiimbotPay(jsonStr: String) {
        try{
            val event = JSON.parseObject(jsonStr)
            val title = event.getString("title")
            val sku = event.getString("sku")
            val code = event.getString("code")
            val price = event.getFloat("price") ?: 0f
            val priceBean = PriceBean(0, price, price, price, title = title, couponCount = 0, couponMaxQuota = "0", gifts = null, productId = code)
            if (code.isNullOrEmpty()) {
                LogUtils.e("========NiimbotWebPay=========:订单code不能为空")
                return
            }
            LoginDataEnum.mainAccountCheck(this) {
                start2GetResult(Intent(this, NiimbotWebPayActivity::class.java).apply {
                    putExtra("price_bean", any2Json(priceBean))
                }) { resultCode, intent ->
                    Log.i("wang", "pay result=$resultCode")
                    MainScope().launch {
                        val params = com.niimbot.fastjson.JSONObject().apply {
                            put("isSuccess", if (resultCode == RESULT_OK) 1 else 0)
                            put("orderId",  intent?.getStringExtra("orderId"))
                        }
                        wv_base?.evaluateJavascript("javascript:notifyPayResult('${params.toJson()}')", null)
                    }
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    fun toDownload(jsonStr: String) {
        try{
            WebPrintHelper.downloadForWeb(jsonStr)
        } catch(e: Exception) {e.printStackTrace()}
    }

    fun sendConnectChange(jsonStr: String) {
        LogUtils.e("===========js event: sendConnectChange: $jsonStr")
        wv_base?.evaluateJavascript("javascript:sendConnectChangeEvent($jsonStr)", null)
    }

    fun sendPrintComplete(jsonStr: String) {
        LogUtils.e("===========js event: printComplete: $jsonStr")
        wv_base?.evaluateJavascript("javascript:printComplete($jsonStr)", null)
    }

    fun sendUploadEvent(jsonStr: String) {
        LogUtils.e("===========js event: sendUploadEvent: $jsonStr")
        wv_base?.evaluateJavascript("javascript:uploadExcelCallback($jsonStr)", null)
    }

    fun jcdy_StatusBar_Color(data: String) {
        /**
         * 0: 深色（文字白色）
         * 1：浅色（文字黑色）
         */
        com.southcity.watermelon.util.LogUtils.e("jcdy_StatusBar_Color data = $data")
        GlobalScope.launch(Dispatchers.Main) {
            try {
                val bean = json2Any(data, NewStoreFragment.StatusBean::class.java)
                    ?: NewStoreFragment.StatusBean()
                EventBus.getDefault().post(ShopStoreBeanEvent(bean));
                com.niimbot.appframework_library.utils.AppUtils.setStatusBarLightColor(this@NiimbotBaseWebActivity, Color.parseColor(bean.color), bean.type == "1")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun requestLocationPermission() {
        MainScope().launch {
            PermissionDialogUtils.showLocationPermissionDialog(this@NiimbotBaseWebActivity, RequestCode.MORE, object : XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    wv_base?.evaluateJavascript("javascript:hasLocationPermission(1)", null)
                }

                override fun onPermissionDenied(
                    deniedPermissions: Array<out String>?,
                    alwaysDenied: Boolean
                ) {
                    wv_base?.evaluateJavascript("javascript:hasLocationPermission(0)", null)
                }

            })
        }
    }

    fun jcdy_shopScan(data:String){
        val event = JSON.parseObject(data)
        val title = event.getString("title")
        val content = event.getString("content")
        val hideTitle = event.getIntValue("hideTitle")
        val hideContent = event.getIntValue("hideContent")
        val hideGallery = event.getIntValue("hideGallery")
        if (NetworkUtils.isConnected()) {
            PermissionDialogUtils.showCameraPermissionDialog(
                this,
                RequestCode.MORE,
                object : XPermissionUtils.OnPermissionListener {
                    override fun onPermissionGranted() {
                        LeMessageManager.getInstance().dispatchMessage(
                            LeMessage(
                                LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                                SearchScanActivityConfig(this@NiimbotBaseWebActivity).apply {
                                    setIntentFlag(LeIntentConfig.IntentFlag.START_ACTIVITY_FOR_RESULT)
                                    intent.putExtra(Constant.SCAN_CREATE, true)
                                    intent.putExtra("trackSource", 5)
                                    intent.putExtra(
                                        NiimbotConstant.TAG_TITLE,
                                        LanguageUtil.checkLanguage("app01099")
                                    )
                                    intent.putExtra(
                                        NiimbotConstant.TAG_CONTENT,
                                        LanguageUtil.checkLanguage("app01098")
                                    )
                                    intent.putExtra(
                                        NiimbotConstant.TAG_HIDE_TILE,
                                        hideTitle == 1
                                    )
                                    intent.putExtra(
                                        NiimbotConstant.TAG_HIDE_CONTENT,
                                        hideContent == 1
                                    )
                                    intent.putExtra(
                                        NiimbotConstant.TAG_HIDE_GALLERY,
                                        hideGallery == 1
                                    )
                                    setRequestCode(222) { resultCode, intent ->
                                        if(resultCode == RESULT_OK) {
                                            val bundle = intent?.extras
                                            val resultStr = bundle?.getString(Constant.newScan_from.RESULT_QRCODE_STRING)
                                            wv_base?.loadUrl("javascript:returnScanResult('$resultStr')")
                                        }
                                    }

                                })
                        )
                    }

                    override fun onPermissionDenied(
                        deniedPermissions: Array<String>,
                        alwaysDenied: Boolean
                    ) {
                        com.niimbot.appframework_library.utils.showToast("app01309")
                    }
                })
        } else {
            melon.south.com.baselibrary.util.showToast("app01139")
        }
    }

    fun openIonic(jsonStr: String) {
        try{
            var json = JSONObject(jsonStr)
            val appId = json.optString("appId")
            if (!appId.isNullOrEmpty()) {
                NiimbotGlobal.gotoUniapp(appId, params = jsonStr)
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    override val hostContext: Context
        get() = this

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        super.startActivityForResult(intent, requestCode)
    }

    override fun getFileProviderAuthority(): String = "com.gengcon.android.jccloudprinter.fileProvider"

    override fun createImageFile(): File? = try {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        getExternalFilesDir(null)?.let {
            File.createTempFile(imageFileName, ".jpg", it)
        }
    } catch (e: IOException) { null }

    override fun createVideoFile(): File? = try {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val videoFileName = "MP4_" + timeStamp + "_"
        getExternalFilesDir(null)?.let {
            File.createTempFile(videoFileName, ".mp4", it)
        }
    } catch (e: IOException) { null }

    override fun hostActivity(): Activity = this
}
